<?php
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

$projects = [];
$contracts = [];
$selectedItem = null;
$achievementReports = [];
$selectedReport = null;
$error_message = '';
$success_message = '';

// Set current page for sidebar highlighting
$currentPage = '/pages/create_merit_reports.php';

if (isset($_SESSION['success_message'])) {
    $success_message = $_SESSION['success_message'];
    unset($_SESSION['success_message']);
}

try {
    $file = fopen(__DIR__ . "/connection/one.txt", "r");
    if (!$file) throw new Exception('خطأ في قراءة ملف الإعدادات');
    
    $servername = trim(fgets($file));
    $username = trim(fgets($file));
    $password = trim(fgets($file));
    $dbname = trim(fgets($file));
    fclose($file);

    $conn = new mysqli($servername, $username, $password, $dbname);
    $conn->set_charset("utf8");

    if ($conn->connect_error) {
        throw new Exception("فشل الاتصال بقاعدة البيانات: " . $conn->connect_error);
    }

    if (isset($_GET['action']) && $_GET['action'] === 'get_contracts' && isset($_GET['project_id'])) {
        header('Content-Type: application/json');
        $projectId = intval($_GET['project_id']);
        
        // First, let's verify the tables exist
        $tables_check = $conn->query("
            SELECT TABLE_NAME 
            FROM information_schema.TABLES 
            WHERE TABLE_SCHEMA = '$dbname' 
            AND TABLE_NAME IN ('contract', 'employees', 'extension_contract')
        ");
        
        $existing_tables = [];
        while($row = $tables_check->fetch_assoc()) {
            $existing_tables[] = $row['TABLE_NAME'];
        }
        
        if (count($existing_tables) < 3) {
            $missing_tables = array_diff(['contract', 'employees', 'extension_contract'], $existing_tables);
            echo json_encode([
                'error' => 'Missing tables: ' . implode(', ', $missing_tables)
            ]);
            exit;
        }

        // Let's try a simpler query first to isolate the issue
        $stmt = $conn->prepare("
            SELECT 
                c.id_contract AS main_id,
                c.name_Job,
                c.version_date AS main_version_date
            FROM contract c
            WHERE c.id_Project = ? AND c.status_contract != 0
        ");
        
        if ($stmt === false) {
            echo json_encode([
                'error' => 'Prepare failed (simple query): ' . $conn->error
            ]);
            exit;
        }

        try {
            $stmt->bind_param("i", $projectId);
            $stmt->execute();
            $result = $stmt->get_result();
            
            // If we get here, the basic query works. Now let's try the full query
            $stmt = $conn->prepare("
                SELECT 
                    c.id_contract AS main_id,
                    e.name_ar_contract AS main_name,
                    c.name_Job,
                    c.version_date AS main_version_date,
                    ec.id_extension_contract AS extension_id,
                    ec.version_date AS extension_version_date,
                    ec.start_date_contract AS extension_start_date,
                    ec.end_date_contract AS extension_end_date
                FROM contract c
                INNER JOIN employees e ON c.id_employees = e.id_employees
                LEFT JOIN extension_contract ec ON c.id_contract = ec.id_contract
                WHERE c.id_Project = ? AND c.status_contract != 0
                ORDER BY c.id_contract, ec.add_extension_contract DESC
            ");

            if ($stmt === false) {
                echo json_encode([
                    'error' => 'Prepare failed (full query): ' . $conn->error
                ]);
                exit;
            }

            $stmt->bind_param("i", $projectId);
            $stmt->execute();
            $result = $stmt->get_result();
            
            $groupedContracts = [];
            while ($row = $result->fetch_assoc()) {
                $mainId = $row['main_id'];
                if (!isset($groupedContracts[$mainId])) {
                    $groupedContracts[$mainId] = [
                        'main' => [
                            'id' => $row['main_id'],
                            'name' => $row['main_name'],
                            'job' => $row['name_Job'],
                            'version_date' => $row['main_version_date'],
                        ],
                        'extensions' => []
                    ];
                }
                if ($row['extension_id']) {
                    $groupedContracts[$mainId]['extensions'][] = [
                        'id' => $row['extension_id'],
                        'version_date' => $row['extension_version_date'],
                        'start_date' => $row['extension_start_date'],
                        'end_date' => $row['extension_end_date'],
                    ];
                }
            }

            $response = [];
            foreach ($groupedContracts as $group) {
                $main = $group['main'];
                $response[] = [
                    'type' => 'main',
                    'id' => 'contract_' . $main['id'],
                    'text' => sprintf('عقد رقم %d - %s - %s', 
                        $main['id'],
                        $main['name'],
                        date('d-m-Y', strtotime($main['version_date']))
                    ),
                    'extensions' => array_map(function($ext) {
                        return [
                            'type' => 'extension',
                            'id' => 'extension_' . $ext['id'],
                            'text' => sprintf('تمديد رقم %d - %s إلى %s', 
                                $ext['id'],
                                date('d-m-Y', strtotime($ext['start_date'])),
                                $ext['end_date'] ? date('d-m-Y', strtotime($ext['end_date'])) : 'مفتوح'
                            )
                        ];
                    }, $group['extensions'])
                ];
            }
            echo json_encode($response);
            
        } catch (Exception $e) {
            echo json_encode([
                'error' => 'Query execution failed: ' . $e->getMessage()
            ]);
        }
        exit;
    } elseif (isset($_GET['action']) && $_GET['action'] === 'save_merit_report') {
        // Disable error display to ensure only JSON is returned
        ini_set('display_errors', 0);
        error_reporting(0);
        
        // Set JSON content type header
        header('Content-Type: application/json');
        $response = ['success' => false, 'message' => ''];
        
        try {
            // First verify if merit_reports table exists
            $table_check = $conn->query("
                SELECT TABLE_NAME 
                FROM information_schema.TABLES 
                WHERE TABLE_SCHEMA = '$dbname' 
                AND TABLE_NAME = 'merit_reports'
            ");
            
            if ($table_check->num_rows === 0) {
                throw new Exception("Table 'merit_reports' does not exist");
            }

            // Get and validate input data
            $data = json_decode(file_get_contents('php://input'), true);
            
            // Validate required fields
            $requiredFields = [
                'id_achievement_reports' => 'رقم تقرير الإنجاز',
                'id_Project' => 'رقم المشروع',
                'id_contract' => 'رقم العقد',
                'actual_working_days' => 'أيام العمل الفعلية',
                'today_wage' => 'الأجر اليومي',
                'total' => 'إجمالي المبلغ'
            ];
            
            foreach ($requiredFields as $field => $label) {
                if (!isset($data[$field]) || empty($data[$field])) {
                    throw new Exception("الحقل المطلوب غير موجود: $label");
                }
            }
            
            // Convert and validate numeric values
            $id_achievement_reports = filter_var($data['id_achievement_reports'], FILTER_VALIDATE_INT);
            $id_Project = filter_var($data['id_Project'], FILTER_VALIDATE_INT);
            $id_contract = filter_var($data['id_contract'], FILTER_VALIDATE_INT);
            $actual_working_days = filter_var($data['actual_working_days'], FILTER_VALIDATE_INT);
            $today_wage = filter_var($data['today_wage'], FILTER_VALIDATE_FLOAT);
            $total = filter_var($data['total'], FILTER_VALIDATE_FLOAT);
            
            // Properly handle id_extension_contract which can be null
            $id_extension_contract = null;
            if (isset($data['id_extension_contract']) && $data['id_extension_contract'] !== null) {
                $id_extension_contract = filter_var($data['id_extension_contract'], FILTER_VALIDATE_INT);
                if ($id_extension_contract === false) {
                    $id_extension_contract = null;
                }
            }

            if ($id_achievement_reports === false || $id_Project === false || $id_contract === false || 
                $actual_working_days === false || $today_wage === false || $total === false) {
                throw new Exception("قيم غير صالحة في البيانات المدخلة");
            }

            // Calculate total based on actual_working_days and today_wage
            // This ensures the total is correctly calculated even if the client-side calculation is off
            $calculated_total = $actual_working_days * $today_wage;
            
            // Use the calculated total or the provided total, depending on your business logic
            // You may want to validate that they're close to each other
            // For now, we'll use the calculated total to ensure accuracy
            $total = $calculated_total;

            // Check if achievement report already has a merit report
            $check_existing = $conn->prepare("SELECT id_merit_reports FROM merit_reports WHERE id_achievement_reports = ?");
            $check_existing->bind_param("i", $id_achievement_reports);
            $check_existing->execute();
            if ($check_existing->get_result()->num_rows > 0) {
                throw new Exception("يوجد بالفعل تقرير استحقاق لتقرير الإنجاز هذا");
            }

            // Prepare the SQL statement with the new fields
            $insert_query = "
                INSERT INTO merit_reports (
                    id_achievement_reports,
                    id_Project,
                    id_contract,
                    id_extension_contract,
                    actual_working_days,
                    today_wage,
                    total,
                    total_after_discount,
                    tax_rate,
                    predecessor,
                    Insurance
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ";
            
            $stmt = $conn->prepare($insert_query);
            if ($stmt === false) {
                throw new Exception("خطأ في إعداد الاستعلام: " . $conn->error);
            }
            
            // Create a variable for extension_contract to handle null properly
            $extension_contract_param = $id_extension_contract;
            
            // Get the deduction values from the data
            $tax_rate = isset($data['tax_rate']) ? filter_var($data['tax_rate'], FILTER_VALIDATE_FLOAT) : 0;
            $predecessor = isset($data['advances']) ? filter_var($data['advances'], FILTER_VALIDATE_FLOAT) : 0;
            $insurance = isset($data['insurance']) ? filter_var($data['insurance'], FILTER_VALIDATE_FLOAT) : 0;
            
            // Calculate the total after discount
            $tax_amount = ($total * $tax_rate) / 100;
            $total_after_discount = $total - $tax_amount - $predecessor - $insurance;
            
            // Ensure the total_after_discount is not negative
            $total_after_discount = max(0, $total_after_discount);
            
            // Bind parameters including the new fields
            $stmt->bind_param(
                "iiiiddddddd", 
                $id_achievement_reports,
                $id_Project,
                $id_contract,
                $extension_contract_param,
                $actual_working_days,
                $today_wage,
                $total,
                $total_after_discount,
                $tax_rate,
                $predecessor,
                $insurance
            );
            
            if (!$stmt->execute()) {
                throw new Exception("فشل في حفظ تقرير الاستحقاق: " . $stmt->error);
            }
            
            if ($stmt->affected_rows > 0) {
                $response['success'] = true;
                $response['message'] = 'تم حفظ تقرير الاستحقاق بنجاح';
                $response['id'] = $conn->insert_id;
            } else {
                throw new Exception("لم يتم إدخال أي بيانات");
            }
            
        } catch (Exception $e) {
            $response['message'] = 'خطأ: ' . $e->getMessage();
            $response['details'] = [
                'sql_error' => $conn->error,
                'sql_errno' => $conn->errno
            ];
        }
        
        echo json_encode($response);
        exit;
    } elseif (isset($_GET['action']) && $_GET['action'] === 'get_merit_report_for_edit' && isset($_GET['achievement_report_id'])) {
        // Disable error display to ensure only JSON is returned
        ini_set('display_errors', 0);
        error_reporting(0);
        
        // Set JSON content type header
        header('Content-Type: application/json');
        $response = ['success' => false, 'message' => ''];
        
        try {
            $achievementReportId = intval($_GET['achievement_report_id']);
            
            // Get existing merit report data
            $stmt = $conn->prepare("
                SELECT 
                    mr.*,
                    ar.id_achievement_reports,
                    ar.id_Project,
                    ar.id_contract,
                    ar.id_extension_contract,
                    ar.start_date_achievement_reports,
                    ar.end_date_achievement_reports,
                    ar.data_todo_list_achievement,
                    ar.actual_working_days,
                    c.contract_type,
                    c.wage_contract,
                    e.name_ar_contract,
                    p.Project_name
                FROM merit_reports mr
                INNER JOIN achievement_reports ar ON mr.id_achievement_reports = ar.id_achievement_reports
                INNER JOIN contract c ON ar.id_contract = c.id_contract
                INNER JOIN employees e ON c.id_employees = e.id_employees
                INNER JOIN project p ON ar.id_Project = p.id_Project
                WHERE mr.id_achievement_reports = ?
            ");
            
            if ($stmt === false) {
                throw new Exception("خطأ في إعداد الاستعلام: " . $conn->error);
            }
            
            $stmt->bind_param("i", $achievementReportId);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($result->num_rows > 0) {
                $data = $result->fetch_assoc();
                $data['data_todo_list_achievement'] = json_decode($data['data_todo_list_achievement'], true);
                
                $response['success'] = true;
                $response['data'] = $data;
            } else {
                throw new Exception("لم يتم العثور على تقرير الاستحقاق");
            }
            
        } catch (Exception $e) {
            $response['message'] = 'خطأ: ' . $e->getMessage();
        }
        
        echo json_encode($response);
        exit;
    } elseif (isset($_GET['action']) && $_GET['action'] === 'update_merit_report') {
        // Disable error display to ensure only JSON is returned
        ini_set('display_errors', 0);
        error_reporting(0);
        
        // Set JSON content type header
        header('Content-Type: application/json');
        $response = ['success' => false, 'message' => ''];
        
        try {
            // Get and validate input data
            $data = json_decode(file_get_contents('php://input'), true);
            
            // Validate required fields
            $requiredFields = [
                'id_merit_reports' => 'رقم تقرير الاستحقاق',
                'actual_working_days' => 'أيام العمل الفعلية',
                'today_wage' => 'الأجر اليومي',
                'total' => 'إجمالي المبلغ'
            ];
            
            foreach ($requiredFields as $field => $label) {
                if (!isset($data[$field]) || (is_string($data[$field]) && empty($data[$field]))) {
                    throw new Exception("الحقل المطلوب غير موجود: $label");
                }
            }
            
            // Convert and validate numeric values
            $id_merit_reports = filter_var($data['id_merit_reports'], FILTER_VALIDATE_INT);
            $actual_working_days = filter_var($data['actual_working_days'], FILTER_VALIDATE_INT);
            $today_wage = filter_var($data['today_wage'], FILTER_VALIDATE_FLOAT);
            $total = filter_var($data['total'], FILTER_VALIDATE_FLOAT);
            
            if ($id_merit_reports === false || $actual_working_days === false || 
                $today_wage === false || $total === false) {
                throw new Exception("قيم غير صالحة في البيانات المدخلة");
            }

            // Calculate total based on actual_working_days and today_wage
            $calculated_total = $actual_working_days * $today_wage;
            $total = $calculated_total;

            // Get the deduction values
            $tax_rate = isset($data['tax_rate']) ? filter_var($data['tax_rate'], FILTER_VALIDATE_FLOAT) : 0;
            $predecessor = isset($data['advances']) ? filter_var($data['advances'], FILTER_VALIDATE_FLOAT) : 0;
            $insurance = isset($data['insurance']) ? filter_var($data['insurance'], FILTER_VALIDATE_FLOAT) : 0;
            
            // Calculate the total after discount
            $tax_amount = ($total * $tax_rate) / 100;
            $total_after_discount = $total - $tax_amount - $predecessor - $insurance;
            
            // Ensure the total_after_discount is not negative
            $total_after_discount = max(0, $total_after_discount);
            
            // Update the merit report
            $update_query = "
                UPDATE merit_reports SET
                    actual_working_days = ?,
                    today_wage = ?,
                    total = ?,
                    total_after_discount = ?,
                    tax_rate = ?,
                    predecessor = ?,
                    Insurance = ?
                WHERE id_merit_reports = ?
            ";
            
            $stmt = $conn->prepare($update_query);
            if ($stmt === false) {
                throw new Exception("خطأ في إعداد الاستعلام: " . $conn->error);
            }
            
            // Bind parameters
            $stmt->bind_param(
                "idddddi", 
                $actual_working_days,
                $today_wage,
                $total,
                $total_after_discount,
                $tax_rate,
                $predecessor,
                $insurance,
                $id_merit_reports
            );
            
            if (!$stmt->execute()) {
                throw new Exception("فشل في تحديث تقرير الاستحقاق: " . $stmt->error);
            }
            
            if ($stmt->affected_rows > 0) {
                $response['success'] = true;
                $response['message'] = 'تم تحديث تقرير الاستحقاق بنجاح';
            } else {
                // Even if no rows were affected, consider it a success if the query executed properly
                $response['success'] = true;
                $response['message'] = 'تم تحديث تقرير الاستحقاق بنجاح (لا توجد تغييرات)';
            }
            
        } catch (Exception $e) {
            $response['message'] = 'خطأ: ' . $e->getMessage();
            $response['details'] = [
                'sql_error' => $conn->error,
                'sql_errno' => $conn->errno
            ];
        }
        
        echo json_encode($response);
        exit;
    }

    $result = $conn->query("SELECT id_Project, Project_name FROM Project WHERE Project_status = 1");
    while ($row = $result->fetch_assoc()) {
        $projects[] = $row;
    }

    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['contract_id'])) {
        $selected = $_POST['contract_id'];
        if (strpos($selected, 'contract_') === 0) {
            $contractId = substr($selected, 9);
            
            // First verify if contract table exists
            $table_check = $conn->query("
                SELECT TABLE_NAME 
                FROM information_schema.TABLES 
                WHERE TABLE_SCHEMA = '$dbname' 
                AND TABLE_NAME = 'contract'
            ");
            
            if ($table_check->num_rows === 0) {
                throw new Exception("Table 'contract' does not exist");
            }
            
            // Check table structure
            $table_structure = $conn->query("DESCRIBE contract");
            if (!$table_structure) {
                throw new Exception("Could not verify contract table structure: " . $conn->error);
            }

            $query = "
                SELECT 
                    c.id_contract, c.id_employees, c.name_Job, c.contract_type, c.wage_contract,
                    c.version_date, c.start_date_contract, c.end_date_contract, c.data_todo_list_contract,
                    e.name_ar_contract, 'contract' as type 
                FROM contract c 
                LEFT JOIN employees e ON c.id_employees = e.id_employees
                WHERE c.id_contract = ?
            ";
            
            $stmt = $conn->prepare($query);
            
            if ($stmt === false) {
                throw new Exception("Prepare failed for contract query: " . $conn->error . "\nQuery: " . $query);
            }
            
            if (!$stmt->bind_param("i", $contractId)) {
                throw new Exception("Parameter binding failed for contract query: " . $stmt->error);
            }
            
            if (!$stmt->execute()) {
                throw new Exception("Execute failed for contract query: " . $stmt->error);
            }
            
            $result = $stmt->get_result();
            if (!$result) {
                throw new Exception("Failed to get result for contract query: " . $stmt->error);
            }
            
            $selectedItem = $result->fetch_assoc();
            
            if (!$selectedItem) {
                throw new Exception("No contract found with ID: " . $contractId);
            }
            
            // First, let's try a very simple query to verify the achievement reports exist
            $test_query = "
                SELECT ar.id_achievement_reports 
                FROM achievement_reports ar 
                WHERE ar.id_contract = ?
            ";

            if ($stmt = $conn->prepare($test_query)) {
                $stmt->bind_param("i", $contractId);
                $stmt->execute();
                $test_result = $stmt->get_result();
                
                if ($test_result->num_rows > 0) {
                    // If we have achievement reports, let's try the full query in steps
                    $query = "
                        SELECT 
                            ar.*,
                            p.Project_name,
                            c.contract_type,
                            c.wage_contract,
                            (SELECT COUNT(*) 
                             FROM merit_reports mr 
                             WHERE mr.id_achievement_reports = ar.id_achievement_reports) as has_merit_report
                        FROM achievement_reports ar
                        INNER JOIN project p 
                            ON ar.id_Project = p.id_Project
                        INNER JOIN contract c 
                            ON ar.id_contract = c.id_contract
                        WHERE ar.id_contract = ?
                        AND ar.id_extension_contract IS NULL
                        ORDER BY ar.start_date_achievement_reports DESC
                    ";

                    if ($stmt = $conn->prepare($query)) {
                        if (!$stmt->bind_param("i", $contractId)) {
                            throw new Exception("Parameter binding failed: " . $stmt->error);
                        }
                        
                        if (!$stmt->execute()) {
                            throw new Exception("Execute failed: " . $stmt->error);
                        }
                        
                        $result = $stmt->get_result();
                        
                        while ($row = $result->fetch_assoc()) {
                            $achievementReports[] = $row;
                        }
                    } else {
                        throw new Exception("Prepare failed for main query: " . $conn->error);
                    }
                } else {
                    // No achievement reports found for this contract
                    $achievementReports = [];
                }
            } else {
                throw new Exception("Prepare failed for test query: " . $conn->error);
            }
        } elseif (strpos($selected, 'extension_') === 0) {
            $extensionId = substr($selected, 10);
            
            // First verify if the required tables exist
            $tables_check = $conn->query("
                SELECT TABLE_NAME 
                FROM information_schema.TABLES 
                WHERE TABLE_SCHEMA = '$dbname' 
                AND TABLE_NAME IN ('extension_contract', 'contract', 'employees')
            ");
            
            if ($tables_check->num_rows < 3) {
                throw new Exception("Required tables are missing");
            }
            
            // Prepare the query with proper error handling
            $query = "
                SELECT 
                    ec.id_extension_contract, ec.id_contract, ec.version_date, ec.start_date_contract, ec.end_date_contract,
                    c.id_employees, c.name_Job, c.contract_type, c.wage_contract, c.data_todo_list_contract,
                    e.name_ar_contract,
                    'extension' as type,
                    c.id_contract as main_contract_id,
                    e.name_ar_contract as main_contract_name,
                    c.contract_type as main_contract_type,
                    c.wage_contract as main_contract_wage
                FROM extension_contract ec
                JOIN contract c ON ec.id_contract = c.id_contract
                JOIN employees e ON c.id_employees = e.id_employees
                WHERE ec.id_extension_contract = ?
            ";
            
            $stmt = $conn->prepare($query);
            if ($stmt === false) {
                throw new Exception("Failed to prepare extension contract query: " . $conn->error);
            }
            
            if (!$stmt->bind_param("i", $extensionId)) {
                throw new Exception("Failed to bind parameters for extension contract query: " . $stmt->error);
            }
            
            if (!$stmt->execute()) {
                throw new Exception("Failed to execute extension contract query: " . $stmt->error);
            }
            
            $result = $stmt->get_result();
            if (!$result) {
                throw new Exception("Failed to get result for extension contract query: " . $stmt->error);
            }
            
            $selectedItem = $result->fetch_assoc();
            if (!$selectedItem) {
                throw new Exception("No extension contract found with ID: " . $extensionId);
            }
            
            // Restructure the data to match our expected format
            $selectedItem['type'] = 'extension';
            $selectedItem['main_contract'] = [
                'id_contract' => $selectedItem['main_contract_id'],
                'name_ar_contract' => $selectedItem['main_contract_name'],
                'contract_type' => $selectedItem['main_contract_type'],
                'wage_contract' => $selectedItem['main_contract_wage']
            ];
            
            // Now fetch achievement reports for the extension with proper error handling
            $query = "
                SELECT 
                    ar.*,
                    p.Project_name,
                    e.name_ar_contract,
                    c.contract_type,
                    c.wage_contract,
                    (SELECT COUNT(*) 
                     FROM merit_reports mr 
                     WHERE mr.id_achievement_reports = ar.id_achievement_reports) as has_merit_report
                FROM achievement_reports ar
                JOIN project p ON ar.id_Project = p.id_Project
                JOIN contract c ON ar.id_contract = c.id_contract
                JOIN employees e ON c.id_employees = e.id_employees
                WHERE ar.id_contract = ? 
                AND ar.id_extension_contract = ?
                ORDER BY ar.start_date_achievement_reports DESC
            ";
            
            $stmt = $conn->prepare($query);
            if ($stmt === false) {
                throw new Exception("Failed to prepare achievement reports query: " . $conn->error);
            }
            
            if (!$stmt->bind_param("ii", $selectedItem['id_contract'], $extensionId)) {
                throw new Exception("Failed to bind parameters for achievement reports query: " . $stmt->error);
            }
            
            if (!$stmt->execute()) {
                throw new Exception("Failed to execute achievement reports query: " . $stmt->error);
            }
            
            $result = $stmt->get_result();
            if (!$result) {
                throw new Exception("Failed to get result for achievement reports query: " . $stmt->error);
            }
            
            $achievementReports = [];
            while ($row = $result->fetch_assoc()) {
                $achievementReports[] = $row;
            }
        }
    }

    // Handle achievement report selection
    if (isset($_POST['report_id']) && isset($_POST['contract_id'])) {
        $reportId = $_POST['report_id'];
        $contractId = $_POST['contract_id'];
        
        // First, let's refetch the contract details
        if (strpos($contractId, 'contract_') === 0) {
            // Handle main contract
            $mainContractId = substr($contractId, 9);
        $stmt = $conn->prepare("
                SELECT c.*, e.name_ar_contract, 'contract' as type 
                FROM contract c 
                LEFT JOIN employees e ON c.id_employees = e.id_employees
                WHERE c.id_contract = ?
            ");
            $stmt->bind_param("i", $mainContractId);
            $stmt->execute();
            $selectedItem = $stmt->get_result()->fetch_assoc();
            
            if ($selectedItem) {
                // Fetch achievement reports for the contract
                $stmt = $conn->prepare("
                    SELECT ar.*, p.Project_name, e.name_ar_contract, c.contract_type, c.wage_contract,
                           (SELECT COUNT(*) FROM merit_reports mr WHERE mr.id_achievement_reports = ar.id_achievement_reports) as has_merit_report
                    FROM achievement_reports ar
                    JOIN project p ON ar.id_Project = p.id_Project
                    JOIN contract c ON ar.id_contract = c.id_contract
                    JOIN employees e ON c.id_employees = e.id_employees
                    WHERE ar.id_contract = ?
                    AND ar.id_extension_contract IS NULL
                    ORDER BY ar.start_date_achievement_reports DESC
                ");
                $stmt->bind_param("i", $mainContractId);
                $stmt->execute();
                $result = $stmt->get_result();
                while ($row = $result->fetch_assoc()) {
                    $achievementReports[] = $row;
                }
            }
        } elseif (strpos($contractId, 'extension_') === 0) {
            // Handle extension contract
            $extensionId = substr($contractId, 10);
            $stmt = $conn->prepare("
                SELECT ec.*, c.*, e.name_ar_contract,
                       'extension' as type,
                       c.id_contract as main_contract_id,
                       e.name_ar_contract as main_contract_name,
                       c.contract_type as main_contract_type,
                       c.wage_contract as main_contract_wage
                FROM extension_contract ec
                JOIN contract c ON ec.id_contract = c.id_contract
                JOIN employees e ON c.id_employees = e.id_employees
                WHERE ec.id_extension_contract = ?
            ");
            $stmt->bind_param("i", $extensionId);
            $stmt->execute();
            $selectedItem = $stmt->get_result()->fetch_assoc();
            
            if ($selectedItem) {
                // Restructure the data
                $selectedItem['type'] = 'extension';
                $selectedItem['main_contract'] = [
                    'id_contract' => $selectedItem['main_contract_id'],
                    'name_ar_contract' => $selectedItem['main_contract_name'],
                    'contract_type' => $selectedItem['main_contract_type'],
                    'wage_contract' => $selectedItem['main_contract_wage']
                ];
                
                // Fetch achievement reports for the extension
                $stmt = $conn->prepare("
                    SELECT ar.*, p.Project_name, e.name_ar_contract, c.contract_type, c.wage_contract,
                           (SELECT COUNT(*) FROM merit_reports mr WHERE mr.id_achievement_reports = ar.id_achievement_reports) as has_merit_report
                    FROM achievement_reports ar
                    JOIN project p ON ar.id_Project = p.id_Project
                    JOIN contract c ON ar.id_contract = c.id_contract
                    JOIN employees e ON c.id_employees = e.id_employees
                    WHERE ar.id_contract = ? 
                    AND ar.id_extension_contract = ?
                    ORDER BY ar.start_date_achievement_reports DESC
                ");
                $stmt->bind_param("ii", $selectedItem['id_contract'], $extensionId);
                $stmt->execute();
                $result = $stmt->get_result();
                while ($row = $result->fetch_assoc()) {
                    $achievementReports[] = $row;
                }
            }
        }
        
        // Now fetch the selected report details
        $query = "
            SELECT 
                ar.*,
                p.Project_name,
                e.name_ar_contract,
                c.contract_type,
                c.wage_contract,
                c.id_contract,
                CASE 
                    WHEN ar.id_extension_contract IS NOT NULL THEN 'extension'
                    ELSE 'contract'
                END as contract_source_type
            FROM achievement_reports ar
            JOIN project p ON ar.id_Project = p.id_Project
            JOIN contract c ON ar.id_contract = c.id_contract
            JOIN employees e ON c.id_employees = e.id_employees
            LEFT JOIN extension_contract ec ON ar.id_extension_contract = ec.id_extension_contract
            WHERE ar.id_achievement_reports = ?
        ";
        
        $stmt = $conn->prepare($query);
        if ($stmt === false) {
            throw new Exception("Failed to prepare statement: " . $conn->error);
        }
        
        $stmt->bind_param("i", $reportId);
        $stmt->execute();
        $selectedReport = $stmt->get_result()->fetch_assoc();
        
        if ($selectedReport) {
            $selectedReport['data_todo_list_achievement'] = json_decode($selectedReport['data_todo_list_achievement'], true);
        }
    }

} catch (Exception $e) {
    $error_message = "خطأ في النظام: " . $e->getMessage();
} finally {
    if (isset($conn)) $conn->close();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء تقرير الاستحقاق - نظام إدارة الموارد البشرية</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
    <link href="../assets/css/sidebar.css" rel="stylesheet">
    
    <style>
        .custom-alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            opacity: 1;
            transition: opacity 0.5s ease-in-out;
        }
        .custom-alert.error {
            background-color: var(--error-bg);
            color: var(--error-text);
            border: 1px solid var(--error-border);
        }
        .custom-alert.success {
            background-color: var(--success-bg);
            color: var(--success-text);
            border: 1px solid var(--success-border);
        }
        .custom-alert.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .custom-alert.fade-out {
            opacity: 0;
        }
        /* Enhanced select field styling */
        .form-group {
            position: relative;
            margin-bottom: 1.5rem;
        }
        .form-select.select2-search {
            height: 48px;
            border: 2px solid #e0e0e0;
            border-radius: 12px;
            padding: 0.625rem 1rem;
            font-size: 1rem;
            transition: all 0.3s ease;
            background-color: #fff;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }
        .form-select.select2-search:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.15);
            outline: none;
        }
        .form-select.select2-search:hover {
            border-color: #bdbdbd;
        }
        .form-label {
            font-size: 0.95rem;
            color: #4a5568;
            margin-bottom: 0.5rem;
            display: block;
        }
        /* Select2 customization */
        .select2-container--default .select2-selection--single {
            height: 48px;
            border: 2px solid #e0e0e0;
            border-radius: 12px;
            background-color: #fff;
            transition: all 0.3s ease;
        }
        .select2-container--default .select2-selection--single:hover {
            border-color: #bdbdbd;
        }
        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 44px;
            padding-right: 1rem;
            padding-left: 1rem;
            color: #2d3748;
        }
        .select2-container--default .select2-selection--single .select2-selection__arrow {
            height: 46px;
            width: 30px;
        }
        .select2-dropdown {
            border: 2px solid #e0e0e0;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .select2-search__field {
            border-radius: 8px !important;
            padding: 0.5rem !important;
        }
        .select2-results__option {
            padding: 0.75rem 1rem;
        }
        .select2-container--default .select2-results__option[aria-selected=true] {
            background-color: rgba(var(--primary-rgb), 0.1);
        }
        .contract-item { 
            padding: 0.75rem 1rem;
            margin-bottom: 0.5rem;
            border-radius: 0.75rem;
            cursor: pointer;
            transition: all 0.2s ease;
            border: 1px solid var(--border-color);
            background-color: var(--bg-card);
            color: var(--text-primary);
        }
        .contract-item:hover { 
            background-color: var(--bg-hover);
            border-color: var(--primary-color);
        }
        .contract-item.selected { 
            background-color: var(--bg-selected);
            border-color: var(--primary-color);
        }
        .extension-item { 
            padding: 0.5rem 2rem;
            margin-bottom: 0.5rem;
            border-radius: 0.75rem;
            background-color: var(--bg-secondary);
            color: var(--text-primary);
        }
        .detail-label { 
            color: var(--text-muted);
            font-weight: 600;
            margin-bottom: 0.25rem;
        }
        .detail-value { 
            color: var(--text-primary);
            font-weight: 500;
        }
        .card {
            background-color: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: 0.75rem;
            box-shadow: var(--card-shadow);
        }
        .card-title {
            color: var(--text-primary);
            font-weight: 600;
            font-size: 1.25rem;
        }
        .form-control, .form-select {
            background-color: var(--bg-input);
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            padding: 0.625rem;
            color: var(--text-primary);
            transition: all 0.2s ease;
        }
        .form-control:focus, .form-select:focus {
            background-color: var(--bg-input);
            border-color: var(--primary-color);
            color: var(--text-primary);
            box-shadow: 0 0 0 0.2rem var(--primary-shadow);
        }
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: var(--text-light);
            padding: 0.625rem 1.25rem;
            font-weight: 500;
            border-radius: 0.5rem;
            transition: all 0.2s ease;
        }
        .btn-primary:hover {
            background-color: var(--primary-hover);
            border-color: var(--primary-hover);
        }
        .btn-primary:focus {
            box-shadow: 0 0 0 0.2rem var(--primary-shadow);
        }
        .btn-warning {
            background-color: #ffc107;
            border-color: #ffc107;
            color: #212529;
            padding: 0.625rem 1.25rem;
            font-weight: 500;
            border-radius: 0.5rem;
            transition: all 0.2s ease;
        }
        .btn-warning:hover {
            background-color: #ffca2c;
            border-color: #ffc720;
            color: #212529;
        }
        .btn-warning:focus {
            box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25);
        }

        /* Edit mode styling */
        .edit-mode-indicator {
            border-left: 4px solid #ffc107;
            background-color: rgba(255, 193, 7, 0.1);
            padding: 1rem;
            margin-bottom: 1rem;
            border-radius: 0.5rem;
        }

        .edit-mode-indicator .alert-heading {
            color: #856404;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .edit-mode-indicator .alert-text {
            color: #856404;
            margin: 0;
        }
    </style>
    <style>
        :root {
            --select-bg: #fff;
            --select-text: #495057;
            --select-border: #ced4da;
            --select-placeholder: #999;
            --select-hover-bg: #f8f9fa;
            --select-focus-border: #86b7fe;
            --select-focus-shadow: rgba(13, 110, 253, 0.25);
        }

        [data-theme="dark"] {
            --select-bg: #2b3035;
            --select-text: #e9ecef;
            --select-border: #495057;
            --select-placeholder: #6c757d;
            --select-hover-bg: #343a40;
            --select-focus-border: #0d6efd;
            --select-focus-shadow: rgba(13, 110, 253, 0.25);
        }

        /* Select2 RTL Fixes with Theme Support */
        .select2-container {
            width: 100% !important;
        }
        
        .select2-container--bootstrap-5 .select2-selection {
            min-height: 38px !important;
            display: flex !important;
            align-items: center !important;
            border: 1px solid var(--select-border) !important;
            border-radius: 0.375rem !important;
            background-color: var(--select-bg) !important;
            color: var(--select-text) !important;
            transition: border-color 0.2s ease, box-shadow 0.2s ease !important;
        }

        .select2-container--bootstrap-5 .select2-selection--single {
            display: flex !important;
            align-items: center !important;
        }

        .select2-container--bootstrap-5 .select2-selection--single .select2-selection__rendered {
            width: 100% !important;
            padding-right: 8px !important;
            padding-left: 20px !important;
            display: block !important;
            position: static !important;
            overflow: hidden !important;
            text-overflow: ellipsis !important;
            white-space: nowrap !important;
            color: var(--select-text) !important;
        }

        /* Placeholder color */
        .select2-container--bootstrap-5 .select2-selection--single .select2-selection__placeholder {
            color: var(--select-placeholder) !important;
        }

        .select2-container--bootstrap-5 .select2-selection--single .select2-selection__arrow {
            position: absolute !important;
            left: 3px !important;
            right: auto !important;
        }

        .select2-container--bootstrap-5 .select2-selection--single .select2-selection__arrow b {
            border-color: var(--select-text) transparent transparent transparent !important;
        }

        .select2-container--bootstrap-5.select2-container--open .select2-selection--single .select2-selection__arrow b {
            border-color: transparent transparent var(--select-text) transparent !important;
        }

        /* Dropdown styles */
        .select2-container--bootstrap-5 .select2-dropdown {
            background-color: var(--select-bg) !important;
            border: 1px solid var(--select-border) !important;
            border-radius: 0.375rem !important;
            text-align: right !important;
        }

        .select2-container--bootstrap-5 .select2-results__option {
            padding: 6px 12px !important;
            text-align: right !important;
            color: var(--select-text) !important;
        }

        .select2-container--bootstrap-5 .select2-results__option--highlighted[aria-selected] {
            background-color: var(--primary-color) !important;
            color: var(--text-light) !important;
        }

        .select2-container--bootstrap-5 .select2-results__option[aria-selected=true] {
            background-color: var(--primary-color) !important;
            color: var(--text-light) !important;
        }

        /* Fix for extension options indentation */
        .extension-option {
            padding-right: 24px !important;
        }

        /* Focus state */
        .select2-container--bootstrap-5.select2-container--focus .select2-selection,
        .select2-container--bootstrap-5.select2-container--open .select2-selection {
            border-color: var(--select-focus-border) !important;
            box-shadow: 0 0 0 0.25rem var(--select-focus-shadow) !important;
        }

        /* Search field in dropdown */
        .select2-container--bootstrap-5 .select2-search--dropdown .select2-search__field {
            border: 1px solid var(--select-border) !important;
            background-color: var(--select-bg) !important;
            color: var(--select-text) !important;
            border-radius: 0.375rem !important;
            padding: 0.375rem 0.75rem !important;
        }

        .select2-container--bootstrap-5 .select2-search--dropdown .select2-search__field:focus {
            border-color: var(--select-focus-border) !important;
            outline: none !important;
        }
    </style>
    <style>
        /* Contract Tree Structure */
        .contract-tree-item {
            position: relative;
            padding-right: 28px !important;
            margin: 4px 0 !important;
        }

        .contract-extension {
            position: relative;
            padding-right: 42px !important;
            font-size: 0.95em;
        }

        /* Horizontal line */
        .contract-extension::before {
            content: '';
            position: absolute;
            right: 22px;
            top: 50%;
            width: 16px;
            height: 2px;
            background-color: var(--select-text);
            opacity: 0.6;
        }

        /* Vertical line */
        .contract-extension::after {
            content: '';
            position: absolute;
            right: 22px;
            top: -12px;
            width: 2px;
            height: calc(100% + 12px);
            background-color: var(--select-text);
            opacity: 0.6;
        }

        /* Special cases for first and last extensions */
        .contract-extension.first-extension::after {
            top: 50%;
            height: calc(50% + 12px);
        }

        .contract-extension.last-extension::after {
            height: 50%;
        }

        /* Main contract styling */
        .contract-main {
            font-weight: 600;
            position: relative;
            margin-top: 8px !important;
        }

        /* Dot for main contract */
        .contract-main::before {
            content: '';
            position: absolute;
            right: 4px;
            top: 50%;
            transform: translateY(-50%);
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: var(--select-text);
            opacity: 0.8;
        }

        /* Arrow for extensions */
        .contract-extension::after {
            content: '';
            position: absolute;
            right: 22px;
            top: -12px;
            width: 2px;
            height: calc(100% + 12px);
            background-color: var(--select-text);
            opacity: 0.6;
        }

        /* Hover effects */
        .select2-container--bootstrap-5 .select2-results__option--highlighted[aria-selected] .contract-extension::before,
        .select2-container--bootstrap-5 .select2-results__option--highlighted[aria-selected] .contract-extension::after,
        .select2-container--bootstrap-5 .select2-results__option[aria-selected=true] .contract-extension::before,
        .select2-container--bootstrap-5 .select2-results__option[aria-selected=true] .contract-extension::after {
            background-color: var(--text-light);
            opacity: 1;
        }

        .select2-container--bootstrap-5 .select2-results__option--highlighted[aria-selected] .contract-main::before,
        .select2-container--bootstrap-5 .select2-results__option[aria-selected=true] .contract-main::before {
            background-color: var(--text-light);
            opacity: 1;
        }

        /* Spacing between groups */
        .select2-results__option + .select2-results__option {
            margin-top: 4px !important;
        }

        /* Container padding for better visibility */
        .select2-container--bootstrap-5 .select2-results__options {
            padding: 8px 4px !important;
        }

        /* Hover state enhancement */
        .select2-container--bootstrap-5 .select2-results__option--highlighted[aria-selected] {
            background-color: var(--primary-color) !important;
            color: var(--text-light) !important;
            transition: all 0.2s ease;
        }
    </style>
    <style>
        /* Achievement Reports Styles */
        .achievement-reports {
            height: 300px; /* Reduced from 400px to 300px */
            overflow-y: auto;
            padding: 0.5rem;
            margin: -0.5rem;
            scrollbar-width: thin;
            scrollbar-color: var(--border-color) transparent;
        }
        
        .achievement-report-container {
            background: var(--card-bg);
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .achievement-report-container:last-child {
            margin-bottom: 0 !important; /* Remove margin from last item */
        }

        .achievement-report-main {
            background-color: var(--bg-light);
            border-color: var(--border-color) !important;
        }

        .achievement-report-main:hover {
            background-color: var(--bg-hover);
        }

        .achievement-report-main.selected {
            background-color: var(--primary-color);
            border-color: var(--primary-color) !important;
        }

        .achievement-report-main.selected .report-info,
        .achievement-report-main.selected .record-id,
        .achievement-report-main.selected .record-separator,
        .achievement-report-main.selected .record-period {
            color: var(--text-light) !important;
        }

        /* New styles for disabled buttons */
        .select-report.disabled,
        .select-report:disabled {
            background-color: var(--bg-secondary);
            border-color: var(--border-color);
            color: var(--text-muted);
            opacity: 0.7;
            cursor: not-allowed;
        }

        .select-report.disabled:hover,
        .select-report:disabled:hover {
            background-color: var(--bg-secondary);
            border-color: var(--border-color);
            transform: none;
            box-shadow: none;
        }

        /* Add a subtle icon to indicate disabled state */
        .select-report.disabled::before,
        .select-report:disabled::before {
            content: '✓';
            margin-right: 0.5rem;
            font-weight: bold;
        }

        /* Existing styles continue below */
        .report-info {
            font-size: 0.95rem;
            color: var(--text-color);
        }

        .record-id {
            font-weight: 600;
            color: var(--primary-color);
        }

        .record-separator {
            color: var(--text-muted);
            font-weight: 300;
        }

        .record-period {
            color: var(--text-color);
        }

        .select-report {
            min-width: 100px;
        }

        /* Alert styling */
        .alert-info {
            margin: -0.5rem; /* Align with card body padding */
            border-radius: 0 0 8px 8px; /* Round only bottom corners */
            border-left: none;
            border-right: none;
            border-bottom: none;
        }

        /* Scrollbar styling */
        .achievement-reports::-webkit-scrollbar {
            width: 6px;
        }

        .achievement-reports::-webkit-scrollbar-track {
            background: transparent;
        }

        .achievement-reports::-webkit-scrollbar-thumb {
            background-color: var(--border-color);
            border-radius: 3px;
        }

        .achievement-reports::-webkit-scrollbar-thumb:hover {
            background-color: var(--text-muted);
        }

        /* Dark theme support */
        [data-theme="dark"] .achievement-report-main {
            background-color: var(--bg-secondary);
        }

        [data-theme="dark"] .achievement-report-main:hover {
            background-color: var(--bg-hover);
        }

        [data-theme="dark"] .alert-info {
            background-color: var(--dark-info-bg);
            color: var(--dark-info-text);
            border-color: var(--dark-info-border);
        }

        /* Custom scrollbar for Firefox */
        .achievement-reports {
            scrollbar-width: thin;
            scrollbar-color: var(--border-color) transparent;
        }
    </style>
    <style>
        /* Achievement Report Details Section Styles */
        #section-four .card {
            box-shadow: var(--card-shadow);
            transition: all 0.3s ease;
        }

        #section-four .detail-label {
            color: var(--text-muted);
            font-size: 0.9rem;
            margin-bottom: 0.25rem;
        }

        #section-four .detail-value {
            color: var(--text-color);
            font-weight: 600;
            font-size: 1rem;
        }

        #section-four .table-responsive {
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            margin: 1rem 0;
            background-color: var(--bg-card);
            max-height: 400px;
            overflow-y: auto;
            scrollbar-width: thin;
            scrollbar-color: var(--border-color) transparent;
        }

        #section-four .table-responsive::-webkit-scrollbar {
            width: 6px;
        }

        #section-four .table-responsive::-webkit-scrollbar-track {
            background: transparent;
        }

        #section-four .table-responsive::-webkit-scrollbar-thumb {
            background-color: var(--border-color);
            border-radius: 3px;
        }

        #section-four .table-responsive::-webkit-scrollbar-thumb:hover {
            background-color: var(--text-muted);
        }

        #section-four #achievement-tasks-table {
            margin-bottom: 0;
            background-color: var(--bg-card);
        }

        #section-four #achievement-tasks-table thead {
            position: sticky;
            top: 0;
            z-index: 1;
        }

        #section-four #achievement-tasks-table thead th {
            background-color: var(--bg-secondary);
            color: var(--text-color);
            font-weight: 600;
            border-bottom: 2px solid var(--border-color);
            white-space: nowrap;
            padding: 1rem;
            transition: all 0.3s ease;
        }

        #section-four #achievement-tasks-table tbody td {
            padding: 0.75rem 1rem;
            vertical-align: middle;
            border-color: var(--border-color);
            color: var(--text-color);
            background-color: var(--bg-card);
            transition: all 0.3s ease;
        }

        #section-four #achievement-tasks-table tbody tr:hover td {
            background-color: var(--bg-hover);
        }

        /* Dark theme support */
        [data-theme="dark"] #section-four .table-responsive {
            background-color: var(--dark-bg-card);
            border-color: var(--dark-border-color);
            scrollbar-color: var(--dark-border-color) var(--dark-bg-card);
        }

        [data-theme="dark"] #section-four .table-responsive::-webkit-scrollbar-thumb {
            background-color: var(--dark-border-color);
        }

        [data-theme="dark"] #section-four .table-responsive::-webkit-scrollbar-thumb:hover {
            background-color: var(--dark-text-muted);
        }

        [data-theme="dark"] #section-four #achievement-tasks-table {
            background-color: var(--dark-bg-card);
            color: var(--dark-text-color);
        }

        [data-theme="dark"] #section-four #achievement-tasks-table thead th {
            background-color: var(--dark-bg-secondary);
            color: var(--dark-text-color);
            border-color: var(--dark-border-color);
        }

        [data-theme="dark"] #section-four #achievement-tasks-table tbody td {
            background-color: var(--dark-bg-card);
            border-color: var(--dark-border-color);
            color: var(--dark-text-color);
        }

        [data-theme="dark"] #section-four #achievement-tasks-table tbody tr:hover td {
            background-color: var(--dark-bg-hover);
        }

        /* Table border styles */
        #section-four #achievement-tasks-table {
            border-collapse: separate;
            border-spacing: 0;
        }

        #section-four #achievement-tasks-table th:first-child,
        #section-four #achievement-tasks-table td:first-child {
            border-right: none;
        }

        #section-four #achievement-tasks-table th:last-child,
        #section-four #achievement-tasks-table td:last-child {
            border-left: none;
        }

        #section-four #achievement-tasks-table tr:last-child td {
            border-bottom: none;
        }

        /* Additional dark theme CSS variables */
        [data-theme="dark"] {
            --dark-bg-card: #2b3035;
            --dark-bg-secondary: #343a40;
            --dark-text-color: #e9ecef;
            --dark-border-color: #495057;
            --dark-bg-hover: #3d4246;
        }
    </style>
    <style>
        /* Merit Calculation Fields Styles */
        .merit-calculation {
            background-color: var(--bg-card);
            border-top: 1px solid var(--border-color);
            padding-top: 2rem;
            margin-top: 2rem;
        }

        .merit-field {
            background-color: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 0.75rem;
            padding: 1.25rem;
            height: 100%;
            transition: all 0.3s ease;
        }

        .merit-field:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .merit-field .detail-label {
            color: var(--text-muted);
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
            display: block;
        }

        .merit-field .merit-value {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .merit-field .detail-value {
            color: var(--text-color);
            font-size: 1.1rem;
            font-weight: 600;
        }

        /* Saved Data Section Styles */
        .saved-data {
            background-color: var(--bg-card);
            border-radius: 0.75rem;
            margin-top: 2rem;
            padding-top: 1rem;
        }

        .saved-data h6 {
            color: var(--text-color);
            font-weight: 600;
            margin-bottom: 1.5rem;
        }

        .saved-data .table {
            margin-bottom: 0;
            background-color: var(--bg-card);
        }

        .saved-data .table th {
            background-color: var(--bg-secondary);
            color: var(--text-color);
            font-weight: 600;
            border-color: var(--border-color);
            padding: 1rem;
            vertical-align: middle;
        }

        .saved-data .table td {
            color: var(--text-color);
            border-color: var(--border-color);
            padding: 1rem;
            vertical-align: middle;
        }

        .saved-data .table-responsive {
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            background-color: var(--bg-card);
        }

        .create-merit-btn {
            padding: 0.75rem 2rem;
            font-size: 1.1rem;
            transition: all 0.3s ease;
        }

        .create-merit-btn:not(:disabled):hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(var(--primary-rgb), 0.3);
        }

        .create-merit-btn:disabled {
            opacity: 0.7;
            cursor: not-allowed;
        }

        /* Dark theme support */
        [data-theme="dark"] .saved-data .table {
            background-color: var(--dark-bg-card);
        }

        [data-theme="dark"] .saved-data .table th {
            background-color: var(--dark-bg-secondary);
            color: var(--dark-text-color);
            border-color: var(--dark-border-color);
        }

        [data-theme="dark"] .saved-data .table td {
            color: var(--dark-text-color);
            border-color: var(--dark-border-color);
            background-color: var(--dark-bg-card);
        }

        [data-theme="dark"] .saved-data .table-responsive {
            border-color: var(--dark-border-color);
            background-color: var(--dark-bg-card);
        }
    </style>
    <style>
        /* Fixed height Select2 dropdowns */
        .select2-container--bootstrap-5 .select2-dropdown {
            max-height: 300px;
            overflow-y: auto;
        }

        .select2-container--bootstrap-5 .select2-results__options {
            max-height: 250px !important;
            overflow-y: auto !important;
            scrollbar-width: thin;
            scrollbar-color: var(--select-border) transparent;
        }

        .select2-container--bootstrap-5 .select2-results__options::-webkit-scrollbar {
            width: 6px;
        }

        .select2-container--bootstrap-5 .select2-results__options::-webkit-scrollbar-track {
            background: transparent;
        }

        .select2-container--bootstrap-5 .select2-results__options::-webkit-scrollbar-thumb {
            background-color: var(--select-border);
            border-radius: 3px;
        }

        .select2-container--bootstrap-5 .select2-results__options::-webkit-scrollbar-thumb:hover {
            background-color: var(--select-text);
        }

        /* Ensure dropdown options are fully visible */
        .select2-container--bootstrap-5 .select2-results__option {
            white-space: normal;
            word-wrap: break-word;
        }

        /* Improve dropdown container appearance */
        .select2-container--bootstrap-5.select2-container--open .select2-dropdown {
            border-color: var(--select-focus-border);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        /* Dark theme support for dropdowns */
        [data-theme="dark"] .select2-container--bootstrap-5 .select2-results__options::-webkit-scrollbar-thumb {
            background-color: var(--dark-border-color);
        }

        [data-theme="dark"] .select2-container--bootstrap-5 .select2-results__options::-webkit-scrollbar-thumb:hover {
            background-color: var(--dark-text-muted);
        }
    </style>
    <style>
        /* Section styling */
        .section-container {
            border: 1px solid var(--border-color);
            border-radius: 0.375rem;
            background-color: #f8f9fa;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }

        [data-theme="dark"] .section-container {
            background-color: #2d303d;
        }

        /* Card styling with blue accents */
        .card {
            background-color: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: 0.75rem;
            box-shadow: var(--card-shadow);
            margin-bottom: 1.5rem;
        }

        .card-title {
            color: var(--text-primary);
            font-weight: 600;
            font-size: 1.25rem;
            margin-bottom: 1.5rem;
            padding-bottom: 0.75rem;
            border-bottom: 2px solid #0d6efd;
        }

        /* Contract details grid layout */
        .contract-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .detail-group {
            padding: 0.75rem;
            background-color: var(--bg-card);
            border-radius: 0.5rem;
            border: 1px solid var(--border-color);
            transition: all 0.2s ease;
        }

        .detail-group:hover {
            border-color: #0d6efd;
            transform: translateY(-2px);
            box-shadow: 0 2px 4px rgba(13, 110, 253, 0.15);
        }

        .detail-label {
            color: var(--text-muted);
            font-weight: 600;
            margin-bottom: 0.25rem;
            font-size: 0.875rem;
        }

        .detail-value {
            color: var(--text-primary);
            font-weight: 500;
        }

        /* Button styling with gradient */
        .btn-primary {
            background: linear-gradient(45deg, #0d6efd, #0a58ca);
            border: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            font-weight: 600;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
            background: linear-gradient(45deg, #0a58ca, #0d6efd);
        }

        .btn-primary:active {
            transform: translateY(1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        /* Alert styling */
        .alert-info {
            margin: -0.5rem;
            border-radius: 0 0 8px 8px;
            border-left: none;
            border-right: none;
            border-bottom: none;
            background-color: var(--bg-info);
            color: var(--text-info);
            border-color: var(--border-info);
        }

        [data-theme="dark"] .alert-info {
            background-color: var(--dark-info-bg);
            color: var(--dark-info-text);
            border-color: var(--dark-info-border);
        }

        /* Achievement report list styling */
        .achievement-reports {
            height: 300px;
            overflow-y: auto;
            padding: 0.5rem;
            margin: -0.5rem;
            scrollbar-width: thin;
            scrollbar-color: var(--border-color) transparent;
        }

        .achievement-report-container {
            margin-bottom: 0.75rem;
        }

        .achievement-report-main {
            background-color: var(--bg-light);
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            padding: 1rem;
            transition: all 0.2s ease;
        }

        .achievement-report-main:hover {
            background-color: var(--bg-hover);
            border-color: #0d6efd;
            transform: translateY(-2px);
            box-shadow: 0 2px 4px rgba(13, 110, 253, 0.15);
        }

        .achievement-report-main.selected {
            background-color: var(--primary-color);
            border-color: #0d6efd;
            color: var(--text-light);
        }

        /* Merit calculation fields styling */
        .merit-field {
            background-color: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 0.75rem;
            padding: 1.25rem;
            height: 100%;
            transition: all 0.2s ease;
        }

        .merit-field:hover {
            border-color: #0d6efd;
            transform: translateY(-2px);
            box-shadow: 0 2px 4px rgba(13, 110, 253, 0.15);
        }

        /* Table styling */
        .table {
            color: var(--text-primary);
            background-color: var(--bg-card);
            border-color: var(--border-color);
        }

        .table th {
            background-color: var(--bg-secondary);
            color: var(--text-primary);
            font-weight: 600;
            text-align: center;
            vertical-align: middle;
            white-space: nowrap;
            border-bottom: 2px solid #0d6efd;
        }

        .table td {
            vertical-align: middle;
        }
    </style>
    <style>
.achievement-report-main {
    transition: all 0.3s ease;
}

.achievement-report-main:hover:not(.selected) {
    background-color: rgba(13, 110, 253, 0.05);
    border-color: #0d6efd !important;
}

.achievement-report-main.selected {
    background-color: #0d6efd;
    border-color: #0d6efd !important;
    color: white;
}

.achievement-report-main.selected .report-info,
.achievement-report-main.selected .record-id,
.achievement-report-main.selected .record-separator,
.achievement-report-main.selected .record-period {
    color: white !important;
}

.select-report {
    min-width: 120px;
    transition: all 0.3s ease;
}

.select-report:not(.disabled):hover {
    transform: translateX(-5px);
}

.select-report.disabled {
    background-color: #6c757d;
    border-color: #6c757d;
    opacity: 0.65;
    cursor: not-allowed;
        }
    </style>
    <style>
        .report-radio {
            width: 20px;
            height: 20px;
            cursor: pointer;
        }

        .report-radio:disabled {
            cursor: not-allowed;
            opacity: 0.5;
        }

        .status-indicator .badge {
            font-size: 0.9rem;
            padding: 0.5rem 0.75rem;
        }

        .achievement-report-main {
            transition: all 0.3s ease;
        }

        .achievement-report-main:hover:not(:has(input:disabled)) {
            border-color: var(--primary-color) !important;
            background-color: var(--bg-hover);
        }

        .achievement-report-main:has(input:checked) {
            background-color: var(--primary-color);
            border-color: var(--primary-color) !important;
            color: white;
        }

        .achievement-report-main:has(input:checked) .report-info,
        .achievement-report-main:has(input:checked) .record-id,
        .achievement-report-main:has(input:checked) .record-separator,
        .achievement-report-main:has(input:checked) .record-period {
            color: white !important;
        }
    </style>
    <style>
        .btn-select-report {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border-radius: 50px;
            font-weight: 600;
            transition: all 0.3s ease;
            background: linear-gradient(45deg, #2196F3, #1976D2);
            color: white;
            border: none;
            box-shadow: 0 2px 4px rgba(33, 150, 243, 0.3);
        }

        .btn-select-report:not(.disabled):hover {
            transform: translateX(-5px);
            background: linear-gradient(45deg, #1976D2, #2196F3);
            box-shadow: 0 4px 8px rgba(33, 150, 243, 0.4);
        }

        .btn-select-report:not(.disabled):active {
            transform: translateX(-2px);
            box-shadow: 0 1px 2px rgba(33, 150, 243, 0.4);
        }

        .btn-select-report.disabled {
            background: linear-gradient(45deg, #9E9E9E, #757575);
            cursor: not-allowed;
            opacity: 0.7;
        }

        .btn-select-report i {
            font-size: 1.1rem;
            transition: transform 0.3s ease;
        }

        .btn-select-report:not(.disabled):hover i {
            transform: translateX(-3px);
        }

        .achievement-report-main {
            border: 2px solid transparent !important;
            transition: all 0.3s ease;
            background: var(--bg-card);
        }

        .achievement-report-main:hover:not(:has(.btn-select-report.disabled)) {
            border-color: #2196F3 !important;
            transform: translateX(-5px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .achievement-report-main:has(.btn-select-report.disabled) {
            opacity: 0.9;
            background: var(--bg-secondary);
        }

        .status-indicator .badge {
            font-size: 0.85rem;
            padding: 0.5rem 0.75rem;
            border-radius: 50px;
            background: linear-gradient(45deg, #4CAF50, #388E3C);
            box-shadow: 0 2px 4px rgba(76, 175, 80, 0.3);
        }
    </style>
    <style>
        /* Remove any previous btn-select-report styles */
        .achievement-report-main .btn-primary {
            background: linear-gradient(45deg, #0d6efd, #0a58ca);
            border: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            font-weight: 600;
            padding: 0.625rem 1.25rem;
            border-radius: 0.5rem;
        }

        .achievement-report-main .btn-primary:not(.disabled):hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
            background: linear-gradient(45deg, #0a58ca, #0d6efd);
        }

        .achievement-report-main .btn-primary:not(.disabled):active {
            transform: translateY(1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .achievement-report-main .btn-primary.disabled {
            background: linear-gradient(45deg, #0d6efd, #0a58ca);
            opacity: 0.7;
            cursor: not-allowed;
            transform: none;
        }

        /* Remove the status-indicator styles since we won't be using them anymore */
        .status-indicator {
            display: none;
        }

        .achievement-report-main {
            transition: all 0.3s ease;
        }

        .achievement-report-main:hover:not(:has(.btn-primary.disabled)) {
            border-color: var(--primary-color) !important;
            background-color: var(--bg-hover);
        }

        .achievement-report-main:has(.btn-primary.disabled) {
            opacity: 0.95;
        }
    </style>
    <style>
        .achievement-report-main {
            transition: all 0.3s ease;
        }

        .achievement-report-main.used-report {
            background-color: var(--bg-secondary);
            border-color: var(--border-color) !important;
            opacity: 0.9;
        }

        .achievement-report-main.used-report .btn-primary {
            background-color: #198754;
            border-color: #198754;
            pointer-events: none;
        }

        .achievement-report-main.used-report .btn-primary i {
            color: #fff;
        }

        .achievement-report-main.used-report:hover {
            transform: none;
            box-shadow: none;
        }

        /* Edit button styles */
        .btn-edit-report {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            border: 2px solid var(--primary-color);
            background-color: transparent;
            color: var(--primary-color);
        }

        .btn-edit-report:hover {
            background-color: var(--primary-color);
            color: white;
            transform: scale(1.1);
            box-shadow: 0 4px 8px rgba(var(--primary-rgb), 0.3);
        }

        .btn-edit-report:active {
            transform: scale(0.95);
        }

        .btn-edit-report i {
            font-size: 1rem;
        }

        /* Success button styles */
        .btn-success.disabled {
            background-color: #198754;
            border-color: #198754;
            opacity: 1;
        }

        #section-four {
            transition: opacity 0.5s ease;
        }

        .fade-out {
            opacity: 0 !important;
        }

        .fade-in {
            opacity: 1 !important;
        }

        @keyframes highlight-success {
            0% {
                background-color: rgba(25, 135, 84, 0.1);
            }
            50% {
                background-color: rgba(25, 135, 84, 0.2);
            }
            100% {
                background-color: var(--bg-secondary);
            }
        }

        .achievement-report-main.highlight-success {
            animation: highlight-success 1s ease forwards;
        }
    </style>
    <style>
        /* CSS for sidebar and layout */
        #content {
            margin-right: 260px;
            transition: 0.3s;
        }

        #sidebar {
            background-color: var(--bg-sidebar);
            color: var(--text-color);
            width: 260px;
            position: fixed;
            top: 0;
            right: 0;
            height: 100vh;
            overflow-y: auto;
            z-index: 1000;
            transition: 0.3s;
            padding-top: 20px;
        }

        /* Remove non-stored-field class since all fields are now stored in the database */
        /* .non-stored-field {
            background-color: rgba(0, 0, 0, 0.03);
        }
        
        [data-theme="dark"] .non-stored-field {
            background-color: rgba(255, 255, 255, 0.05);
        } */

        .logo {
            text-align: center;
            padding: 10px 0;
            margin-bottom: 20px;
            font-size: 20px;
            font-weight: bold;
        }
    </style>
</head>
<body data-theme="light">
    <?php
    // Include the sidebar
    include 'sidebar.php';
    ?>
    
    <main id="content">
        <div class="container-fluid py-4">
            <?php if (!empty($error_message)): ?>
                <div class="custom-alert error" role="alert">
                    <?= $error_message ?>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($success_message)): ?>
                <div class="custom-alert success" role="alert">
                    <?= $success_message ?>
                </div>
            <?php endif; ?>

            <!-- Update the informational message -->
            <div class="alert alert-info mb-4" role="alert">
                <i class="bi bi-info-circle me-2"></i>
                مرحباً بك في صفحة إنشاء تقارير الاستحقاق! هذه الصفحة تتيح لك إنشاء تقارير استحقاق للموظفين بناءً على تقارير الإنجاز الخاصة بهم. يمكنك اختيار المشروع والعقد المطلوب، ثم اختيار تقرير الإنجاز المناسب لإنشاء تقرير الاستحقاق الخاص به. سيتم حساب المبالغ المستحقة تلقائياً بناءً على نوع العقد وفترة العمل.
            </div>

            <div class="card mb-4">
                <div class="card-body">
                    <h5 class="card-title mb-4">البحث عن العقود</h5>
                    <form method="post" id="searchForm">
                        <div class="row g-4">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="project_id" class="form-label fw-bold mb-2">اختر مشروع</label>
                                    <select class="form-select select2-search" id="project_id" name="project_id" required>
                                        <option value="">اختر المشروع</option>
                                        <?php foreach ($projects as $project): ?>
                                            <option value="<?= $project['id_Project'] ?>" <?= isset($_POST['project_id']) && $_POST['project_id'] == $project['id_Project'] ? 'selected' : '' ?>>
                                                <?= htmlspecialchars($project['Project_name']) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="contract_id" class="form-label fw-bold mb-2">اختر عقد</label>
                                    <select class="form-select select2-search" id="contract_id" name="contract_id" required <?= empty($_POST['project_id']) ? 'disabled' : '' ?>>
                                        <option value="">اختر العقد</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-12 mt-3">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-search me-1"></i>
                                    بحث
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <?php if ($selectedItem): ?>
                <div class="card mb-4">
                    <div class="card-body">
                        <h5 class="card-title">تفاصيل العقد</h5>
                        <?php if ($selectedItem['type'] === 'contract'): ?>
                            <div class="contract-details">
                                <div class="detail-group">
                                    <div class="detail-label">رقم العقد</div>
                                    <div class="detail-value"><?= $selectedItem['id_contract'] ?></div>
                                </div>
                                <div class="detail-group">
                                    <div class="detail-label">اسم صاحب العقد</div>
                                    <div class="detail-value"><?= htmlspecialchars($selectedItem['name_ar_contract']) ?></div>
                                </div>
                                <div class="detail-group">
                                    <div class="detail-label">نوع العقد</div>
                                    <div class="detail-value"><?= ($selectedItem['contract_type'] == 1 ? 'أجر شهري' : 'أجر يومي') ?></div>
                                </div>
                                <div class="detail-group">
                                    <div class="detail-label">المبلغ</div>
                                    <div class="detail-value"><?= $selectedItem['wage_contract'] ?></div>
                                </div>
                                <div class="detail-group">
                                    <div class="detail-label">تاريخ الإصدار</div>
                                    <div class="detail-value"><?= date('Y-m-d', strtotime($selectedItem['version_date'])) ?></div>
                                </div>
                                <div class="detail-group">
                                    <div class="detail-label">تاريخ البداية</div>
                                    <div class="detail-value"><?= date('Y-m-d', strtotime($selectedItem['start_date_contract'])) ?></div>
                                </div>
                                <div class="detail-group">
                                    <div class="detail-label">تاريخ النهاية</div>
                                    <div class="detail-value">
                                        <?= $selectedItem['end_date_contract'] ? date('Y-m-d', strtotime($selectedItem['end_date_contract'])) : 'مفتوح' ?>
                                    </div>
                                </div>
                            </div>
                        <?php elseif ($selectedItem['type'] === 'extension'): ?>
                            <div class="contract-details">
                                <div class="detail-group">
                                    <div class="detail-label">رقم العقد الأساسي</div>
                                    <div class="detail-value"><?= $selectedItem['main_contract']['id_contract'] ?></div>
                                </div>
                                <div class="detail-group">
                                    <div class="detail-label">رقم التمديد</div>
                                    <div class="detail-value"><?= $selectedItem['id_extension_contract'] ?></div>
                                </div>
                                <div class="detail-group">
                                    <div class="detail-label">اسم صاحب العقد</div>
                                    <div class="detail-value"><?= htmlspecialchars($selectedItem['main_contract']['name_ar_contract']) ?></div>
                                </div>
                                <div class="detail-group">
                                    <div class="detail-label">نوع العقد</div>
                                    <div class="detail-value"><?= ($selectedItem['main_contract']['contract_type'] == 1 ? 'أجر شهري' : 'أجر يومي') ?></div>
                                </div>
                                <div class="detail-group">
                                    <div class="detail-label">المبلغ</div>
                                    <div class="detail-value"><?= $selectedItem['main_contract']['wage_contract'] ?></div>
                                </div>
                                <div class="detail-group">
                                    <div class="detail-label">تاريخ الإصدار</div>
                                    <div class="detail-value"><?= date('Y-m-d', strtotime($selectedItem['version_date'])) ?></div>
                                </div>
                                <div class="detail-group">
                                    <div class="detail-label">تاريخ البداية</div>
                                    <div class="detail-value"><?= date('Y-m-d', strtotime($selectedItem['start_date_contract'])) ?></div>
                                </div>
                                <div class="detail-group">
                                    <div class="detail-label">تاريخ النهاية</div>
                                    <div class="detail-value">
                                        <?= $selectedItem['end_date_contract'] ? date('Y-m-d', strtotime($selectedItem['end_date_contract'])) : 'مفتوح' ?>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Achievement Reports Section -->
                <div class="card mb-4" id="section-three">
                    <div class="card-body">
                        <h5 class="card-title">تقارير الإنجاز المرتبطة</h5>
                        <?php if (!empty($achievementReports)): ?>
                            <div class="achievement-reports">
                                <?php foreach ($achievementReports as $report): ?>
                                    <div class="achievement-report-container mb-2">
                                        <div class="achievement-report-main p-3 border rounded d-flex justify-content-between align-items-center <?= isset($_POST['report_id']) && $_POST['report_id'] == $report['id_achievement_reports'] ? 'selected' : '' ?>">
                                            <div class="d-flex align-items-center">
                                                <div class="report-info">
                                                    <span class="record-id">تقرير <?= htmlspecialchars($report['id_achievement_reports']) ?></span>
                                                    <span class="record-separator mx-2">|</span>
                                                    <span class="record-period">
                                                        الفترة (من <?= date('Y-m-d', strtotime($report['start_date_achievement_reports'])) ?>
                                                        إلى <?= date('Y-m-d', strtotime($report['end_date_achievement_reports'])) ?>)
                                                    </span>
                                                </div>
                                            </div>
                                            <div class="d-flex align-items-center gap-2">
                                                <?php if ($report['has_merit_report'] > 0): ?>
                                                    <button type="button" 
                                                            class="btn btn-success disabled"
                                                            disabled>
                                                        <i class="bi bi-check-circle me-2"></i>
                                                        تم إنشاء التقرير
                                                    </button>
                                                    <button type="button" 
                                                            class="btn btn-outline-primary btn-edit-report"
                                                            title="تعديل التقرير"
                                                            onclick="editReport('<?= $report['id_achievement_reports'] ?>', '<?= isset($_POST['contract_id']) ? htmlspecialchars($_POST['contract_id']) : '' ?>', '<?= isset($report['id_Project']) ? htmlspecialchars($report['id_Project']) : '' ?>')">
                                                        <i class="bi bi-pencil"></i>
                                                    </button>
                                                <?php else: ?>
                                                    <button type="button" 
                                                            class="btn btn-primary"
                                                            onclick="selectReport('<?= $report['id_achievement_reports'] ?>', '<?= isset($_POST['contract_id']) ? htmlspecialchars($_POST['contract_id']) : '' ?>', '<?= isset($report['id_Project']) ? htmlspecialchars($report['id_Project']) : '' ?>')">
                                                        <i class="bi bi-search me-2"></i>
                                                        اختيار التقرير
                                                    </button>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-info mb-0">
                                <i class="bi bi-info-circle me-2"></i>
                                لا توجد تقارير إنجاز مرتبطة بهذا العقد
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>

            <div id="section-four" style="display: none;">
                <?php if ($selectedReport): ?>
                <div class="card mb-4">
                    <div class="card-body">
                        <h5 class="card-title mb-4">بيانات تقرير الإنجاز المحدد</h5>
                        
                        <!-- Report Details -->
                        <div class="row g-4 mb-4">
                            <div class="col-md-4">
                                <div class="detail-label">رقم التقرير</div>
                                <div class="detail-value"><?= htmlspecialchars($selectedReport['id_achievement_reports']) ?></div>
                            </div>
                            <div class="col-md-4">
                                <div class="detail-label">تاريخ البداية</div>
                                <div class="detail-value"><?= date('Y-m-d', strtotime($selectedReport['start_date_achievement_reports'])) ?></div>
                            </div>
                            <div class="col-md-4">
                                <div class="detail-label">تاريخ النهاية</div>
                                <div class="detail-value"><?= date('Y-m-d', strtotime($selectedReport['end_date_achievement_reports'])) ?></div>
                            </div>
                        </div>

                        <!-- Todo List Table -->
                        <h6 class="mb-3">جدول الإنجاز للفترة المحددة</h6>
                        <div class="table-responsive">
                            <table class="table table-bordered" id="achievement-tasks-table">
                                <?php if ($selectedReport['contract_type'] == 1): ?>
                                <!-- Monthly Wage Table -->
                                <thead>
                                    <tr>
                                        <th>اسم المهمة</th>
                                        <th>نسبة الإنجاز الإجمالية حتى الآن</th>
                                        <th>نسبة الإنجاز للفترة المحددة</th>
                                        <th>الملاحظات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php 
                                    if (isset($selectedReport['data_todo_list_achievement']['jobDetails']['tasks'])) {
                                        foreach ($selectedReport['data_todo_list_achievement']['jobDetails']['tasks'] as $task): 
                                    ?>
                                        <tr>
                                            <td><?= htmlspecialchars($task['taskName']) ?></td>
                                            <td><?= htmlspecialchars($task['total']) . ' %' ?></td>
                                            <td><?= htmlspecialchars($task['completionRate']) . ' %' ?></td>
                                            <td><?= htmlspecialchars($task['notes'] ?? '-') ?></td>
                                        </tr>
                                    <?php 
                                        endforeach;
                                    }
                                    ?>
                                </tbody>
                                <?php else: ?>
                                <!-- Daily Wage Table -->
                                <thead>
                                    <tr>
                                        <th>اسم المهمة</th>
                                        <th>إجمالي أيام العمل المنجزة حتى الآن</th>
                                        <th>أيام العمل في الفترة المحددة</th>
                                        <th>الملاحظات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php 
                                    if (isset($selectedReport['data_todo_list_achievement']['jobDetails']['tasks'])) {
                                        foreach ($selectedReport['data_todo_list_achievement']['jobDetails']['tasks'] as $task): 
                                    ?>
                                        <tr>
                                            <td><?= htmlspecialchars($task['taskName']) ?></td>
                                            <td><?= htmlspecialchars($task['total']) ?></td>
                                            <td><?= htmlspecialchars($task['completionRate']) ?></td>
                                            <td><?= htmlspecialchars($task['notes'] ?? '-') ?></td>
                                        </tr>
                                    <?php 
                                        endforeach;
                                    }
                                    ?>
                                </tbody>
                                <?php endif; ?>
                            </table>
                        </div>

                        <!-- Merit Calculation Fields -->
                        <div class="merit-calculation mt-4">
                            <h5 class="card-title">حساب الاستحقاق</h5>
                            <div class="row g-4">
                                <div class="col-md-4">
                                    <div class="merit-field">
                                        <label class="detail-label">قيمة الأجر اليومي</label>
                                        <div class="merit-value">
                                            <?php
                                            $dailyWageAmount = 0;
                                            $wageCalculationStatus = [
                                                'success' => false,
                                                'message' => '',
                                                'details' => []
                                            ];
                                            
                                            try {
                                                // Validate that we have the required data
                                                if (!$selectedReport) {
                                                    throw new Exception("لم يتم العثور على بيانات التقرير");
                                                }

                                                // Get wage directly from the selected report data
                                                $contractWage = floatval($selectedReport['wage_contract']);
                                                
                                                if ($contractWage <= 0) {
                                                    throw new Exception("قيمة الأجر في العقد غير صالحة (القيمة: {$contractWage})");
                                                }

                                                $wageCalculationStatus['details'][] = "تم استرجاع قيمة الأجر من العقد: {$contractWage}";

                                                // Calculate daily wage based on contract type
                                                if ($selectedReport['contract_type'] == 2) { // Daily Wage
                                                    $dailyWageAmount = $contractWage;
                                                    $wageCalculationStatus['details'][] = "عقد بأجر يومي - تم استخدام القيمة مباشرة";
                                                    $wageCalculationStatus['success'] = true;
                                                    $wageCalculationStatus['message'] = "تم تحديد قيمة الأجر اليومي بنجاح";
                                                    
                                                } else { // Monthly Wage
                                                    if (!isset($selectedReport['start_date_achievement_reports'])) {
                                                        throw new Exception("لم يتم العثور على تاريخ بداية التقرير");
                                                    }
                                                    
                                                    $startDate = new DateTime($selectedReport['start_date_achievement_reports']);
                                                    $year = intval($startDate->format('Y'));
                                                    $month = intval($startDate->format('m'));
                                                    $daysInMonth = cal_days_in_month(CAL_GREGORIAN, $month, $year);
                                                    
                                                    $wageCalculationStatus['details'][] = "عقد بأجر شهري - جاري حساب المعدل اليومي";
                                                    $wageCalculationStatus['details'][] = "عدد أيام الشهر ({$month}/{$year}): {$daysInMonth}";
                                                    
                                                    if ($daysInMonth <= 0) {
                                                        throw new Exception("عدد أيام الشهر غير صالح ({$daysInMonth})");
                                                    }
                                                    
                                                    $dailyWageAmount = $contractWage / $daysInMonth;
                                                    $wageCalculationStatus['success'] = true;
                                                    $wageCalculationStatus['message'] = "تم حساب قيمة الأجر اليومي بنجاح";
                                                }
                                                
                                            } catch (Exception $e) {
                                                $wageCalculationStatus['success'] = false;
                                                $wageCalculationStatus['message'] = "خطأ: " . $e->getMessage();
                                                $wageCalculationStatus['details'][] = "حدث خطأ: " . $e->getMessage();
                                            }
                                            ?>
                                            <div class="d-flex flex-column">
                                                <span class="detail-value"><?= number_format($dailyWageAmount, 2) ?></span>
                                                <?php if ($wageCalculationStatus['message']): ?>
                                                    <div class="mt-2 small <?= $wageCalculationStatus['success'] ? 'text-success' : 'text-danger' ?>">
                                                        <i class="bi <?= $wageCalculationStatus['success'] ? 'bi-check-circle' : 'bi-exclamation-circle' ?>"></i>
                                                        <?= htmlspecialchars($wageCalculationStatus['message']) ?>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="merit-field">
                                        <label class="detail-label">أيام العمل الفعلية</label>
                                        <div class="merit-value">
                                            <?php
                                            $actualWorkingDays = 0;
                                            
                                            if ($selectedReport) {
                                                if ($selectedReport['contract_type'] == 2) { // Daily Wage
                                                    $actualWorkingDays = isset($selectedReport['actual_working_days']) ? 
                                                        intval($selectedReport['actual_working_days']) : 0;
                                                } else { // Monthly Wage
                                                    $startDate = new DateTime($selectedReport['start_date_achievement_reports']);
                                                    $endDate = new DateTime($selectedReport['end_date_achievement_reports']);
                                                    $interval = $startDate->diff($endDate);
                                                    $actualWorkingDays = $interval->days + 1; // Including both start and end dates
                                                }
                                            }
                                            
                                            echo '<span class="detail-value">' . $actualWorkingDays . '</span>';
                                            ?>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="merit-field">
                                        <label class="detail-label">إجمالي مبلغ الاستحقاق قبل الخصومات</label>
                                        <div class="merit-value">
                                            <?php
                                            $totalMeritAmount = $dailyWageAmount * $actualWorkingDays;
                                            echo '<span class="detail-value" id="base-total-amount">' . number_format($totalMeritAmount, 2) . '</span>';
                                            ?>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- New Deduction Fields -->
                            <div class="row g-4 mt-4">
                                <div class="col-md-4">
                                    <div class="merit-field">
                                        <label class="detail-label">نسبة الضريبة (%)</label>
                                        <div class="merit-value">
                                            <input type="number" class="form-control deduction-input" id="tax-rate" 
                                                   min="0" max="100" step="0.01" value="0" 
                                                   oninput="updateTotalAmount()">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="merit-field">
                                        <label class="detail-label">السلف</label>
                                        <div class="merit-value">
                                            <input type="number" class="form-control deduction-input" id="advances" 
                                                   min="0" step="0.01" value="0" 
                                                   oninput="updateTotalAmount()">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="merit-field">
                                        <label class="detail-label">التأمين</label>
                                        <div class="merit-value">
                                            <input type="number" class="form-control deduction-input" id="insurance" 
                                                   min="0" step="0.01" value="0" 
                                                   oninput="updateTotalAmount()">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Final Total Amount -->
                            <div class="row g-4 mt-4">
                                <div class="col-12">
                                    <div class="merit-field">
                                        <label class="detail-label">صافي مبلغ الاستحقاق بعد الخصومات</label>
                                        <div class="merit-value">
                                            <span class="detail-value" id="final-total-amount"><?= number_format($totalMeritAmount, 2) ?></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Saved Data Section -->
                        <div class="saved-data mt-5">
                            <h5 class="card-title">ملخص البيانات</h5>
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th>رقم المشروع</th>
                                            <th>رقم العقد</th>
                                            <th>رقم التمديد</th>
                                            <th>رقم التقرير</th>
                                            <th>قيمة الأجر اليومي</th>
                                            <th>أيام العمل الفعلية</th>
                                            <th>إجمالي مبلغ الاستحقاق قبل الخصومات</th>
                                            <th>صافي مبلغ الاستحقاق بعد الخصومات</th>
                                            <th>نسبة الضريبة (%)</th>
                                            <th>السلف</th>
                                            <th>التأمين</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td id="project-number">-</td>
                                            <td id="contract-number"><?= isset($selectedItem['id_contract']) ? htmlspecialchars($selectedItem['id_contract']) : '-' ?></td>
                                            <td id="extension-number"><?= isset($selectedItem['type']) && $selectedItem['type'] === 'extension' ? htmlspecialchars($selectedItem['id_extension_contract']) : '-' ?></td>
                                            <td id="report-number"><?= isset($selectedReport['id_achievement_reports']) ? htmlspecialchars($selectedReport['id_achievement_reports']) : '-' ?></td>
                                            <td id="daily-wage"><?= $dailyWageAmount > 0 ? number_format($dailyWageAmount, 2) : '-' ?></td>
                                            <td id="working-days"><?= $actualWorkingDays > 0 ? $actualWorkingDays : '-' ?></td>
                                            <td id="total-amount"><?= $totalMeritAmount > 0 ? number_format($totalMeritAmount, 2) : '-' ?></td>
                                            <td id="summary-net-amount"><?= isset($selectedReport['net_amount']) ? number_format($selectedReport['net_amount'], 2) : '-' ?></td>
                                            <td id="summary-tax-rate"><?= isset($selectedReport['tax_rate']) ? number_format($selectedReport['tax_rate'], 2) : '-' ?></td>
                                            <td id="summary-advances"><?= isset($selectedReport['advances']) ? number_format($selectedReport['advances'], 2) : '-' ?></td>
                                            <td id="summary-insurance"><?= isset($selectedReport['insurance']) ? number_format($selectedReport['insurance'], 2) : '-' ?></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <!-- Create/Update Merit Report Button -->
                            <div class="text-center mt-4">
                                <button type="button" class="btn btn-primary btn-lg create-merit-btn" 
                                        <?= ($dailyWageAmount > 0 && $actualWorkingDays > 0) ? '' : 'disabled' ?>
                                        onclick="saveMeritReport()"
                                        data-mode="create">
                                    <i class="bi bi-plus-circle me-2"></i>
                                    إنشاء تقرير الاستحقاق
                                </button>
                                <button type="button" class="btn btn-secondary btn-lg ms-3 cancel-edit-btn" 
                                        onclick="cancelEdit()"
                                        style="display: none;">
                                    <i class="bi bi-x-circle me-2"></i>
                                    إلغاء التعديل
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </main>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script src="../assets/js/sidebar.js"></script>
    <script src="../assets/js/theme.js"></script>
    <script>
        // Theme handling
        function setTheme(theme) {
            document.body.setAttribute('data-theme', theme);
            localStorage.setItem('theme', theme);
            
            // Update theme toggle button icon
            const themeToggle = document.getElementById('theme-toggle');
            if (themeToggle) {
                const icon = themeToggle.querySelector('i');
                if (theme === 'dark') {
                    icon.classList.remove('bi-sun-fill');
                    icon.classList.add('bi-moon-fill');
                } else {
                    icon.classList.remove('bi-moon-fill');
                    icon.classList.add('bi-sun-fill');
                }
            }
        }

        // Initialize theme
        const savedTheme = localStorage.getItem('theme') || 'light';
        setTheme(savedTheme);

        // Theme toggle handler
        document.getElementById('theme-toggle').addEventListener('click', function() {
            const currentTheme = document.body.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            setTheme(newTheme);
        });

        // Function to update saved data section
        function updateSavedDataSection() {
            // Get project number from the selected project option
            const projectId = $('#project_id').val();
            const projectNumber = document.getElementById('project-number');
            
            if (projectNumber) {
                projectNumber.textContent = projectId || '-';
            }
            
            // Get contract and extension details from the selectedItem
            const selectedItem = <?php echo json_encode($selectedItem ?? null); ?>;
            
            // Update the table cells
            if (document.getElementById('contract-number')) {
                document.getElementById('contract-number').textContent = 
                    selectedItem?.type === 'contract' ? selectedItem.id_contract : 
                    selectedItem?.type === 'extension' ? selectedItem.main_contract.id_contract : '-';
            }
            
            if (document.getElementById('extension-number')) {
                document.getElementById('extension-number').textContent = 
                    selectedItem?.type === 'extension' ? selectedItem.id_extension_contract : '-';
            }
        }

        // Initialize Select2
        $(document).ready(function() {
            // Initialize Select2 for project dropdown
            $('#project_id').select2({
                theme: 'bootstrap-5',
                width: '100%',
                dir: 'rtl',
                containerCssClass: 'form-select',
                dropdownCssClass: 'select2-dropdown-rtl',
                placeholder: 'اختر المشروع',
                allowClear: true
            }).on('change', function() {
                updateSavedDataSection();
            });

            // Initialize Select2 for contract dropdown with custom formatting
            $('#contract_id').select2({
                theme: 'bootstrap-5',
                width: '100%',
                dir: 'rtl',
                containerCssClass: 'form-select',
                dropdownCssClass: 'select2-dropdown-rtl',
                placeholder: 'اختر العقد',
                allowClear: true,
                templateResult: formatContract,
                templateSelection: formatContractSelection
            });

            // Custom formatting for contract options in dropdown
            function formatContract(contract) {
                if (!contract.id) {
                    return contract.text;
                }

                var $contract = $(
                    '<span class="contract-tree-item ' + 
                    (contract.element.className.includes('extension-option') ? 'contract-extension' : 'contract-main') + 
                    '">' + contract.text + '</span>'
                );

                return $contract;
            }

            // Custom formatting for selected contract
            function formatContractSelection(contract) {
                if (!contract.id) {
                    return contract.text;
                }
                return contract.text;
            }

            // Project selection change handler
            $('#project_id').on('change', function() {
                const projectId = $(this).val();
                const contractSelect = $('#contract_id');
                
                // Reset and disable contract select if no project selected
                if (!projectId) {
                    contractSelect.empty().prop('disabled', true).trigger('change');
                    return;
                }

                // Enable contract select and load data
                contractSelect.prop('disabled', false);
                
                $.get(`?action=get_contracts&project_id=${projectId}`, function(data) {
                    contractSelect.empty().append('<option value="">اختر العقد</option>');
                    
                    data.forEach(contract => {
                        // Add main contract
                        contractSelect.append($('<option>', {
                            value: contract.id,
                            text: contract.text,
                            class: 'contract-main-option'
                        }));
                        
                        // Add extensions if any
                        if (contract.extensions && contract.extensions.length > 0) {
                            contract.extensions.forEach((ext, index) => {
                                contractSelect.append($('<option>', {
                                    value: ext.id,
                                    text: ext.text,
                                    class: 'extension-option' + 
                                          (index === 0 ? ' first-extension' : '') + 
                                          (index === contract.extensions.length - 1 ? ' last-extension' : '')
                                }));
                            });
                        }
                    });
                    
                    contractSelect.trigger('change');
                });
            });

            // Form submission handler
            $('#searchForm').on('submit', function(e) {
                if (!$(this).find(':submit').hasClass('clicked')) {
                    e.preventDefault();
                    return false;
                }
            });

            // Submit button click handler
            $('#searchForm :submit').on('click', function() {
                $(this).addClass('clicked');
            });

            // Auto-hide alerts after 5 seconds
            setTimeout(function() {
                $('.custom-alert').fadeOut('slow');
            }, 5000);

            // Show section four if it exists and has content
            const sectionFour = document.getElementById('section-four');
            if (sectionFour && sectionFour.innerHTML.trim()) {
                sectionFour.style.display = 'block';
            }

            // Initialize the selected state for the currently selected report
            const urlParams = new URLSearchParams(window.location.search);
            const reportId = urlParams.get('report_id');
            if (reportId) {
                const reportButton = document.querySelector(`[data-report-id="${reportId}"]`);
                if (reportButton) {
                    reportButton.closest('.achievement-report-main').classList.add('selected');
                }
            }

            // Initial update of saved data section
            updateSavedDataSection();
        });

        // Replace the existing selectReport function with this updated version
        function selectReport(reportId, contractId, projectId, callback) {
            // Create form data with the correct contract and project IDs from the clicked report's form
            const formData = new FormData();
            formData.append('contract_id', contractId);
            formData.append('report_id', reportId);
            formData.append('project_id', projectId);
            
            fetch(window.location.pathname, {
                method: 'POST',
                body: formData
            })
            .then(response => response.text())
            .then(html => {
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = html;
                
                const newSectionFour = tempDiv.querySelector('#section-four');
                
                if (newSectionFour) {
                    const currentSectionFour = document.querySelector('#section-four');
                    currentSectionFour.innerHTML = newSectionFour.innerHTML;
                    currentSectionFour.style.display = 'block';
                    
                    // Update project number in summary after loading new content
                    document.getElementById('project-number').textContent = projectId || '-';
                    
                    // Remove "selected" class from all report items
                    document.querySelectorAll('.achievement-report-main').forEach(report => {
                        report.classList.remove('selected');
                    });
                    
                    // Mark this report as selected (find the report container based on the selected report's hidden input)
                    const selectedReportForm = document.querySelector(`form input[value="${reportId}"]`).closest('.achievement-report-main');
                    if (selectedReportForm) {
                        selectedReportForm.classList.add('selected');
                    }
                    
                    // Reinitialize event listeners if needed
                    initializeMeritReportEvents();

                    // Smooth-scroll to section-four
                    currentSectionFour.scrollIntoView({ 
                        behavior: 'smooth',
                        block: 'start'
                    });
                    
                    // Call the callback if provided (for edit mode)
                    if (callback && typeof callback === 'function') {
                        callback();
                    }
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('error', 'حدث خطأ أثناء تحديث البيانات');
            });
        }

        // Add this function to reinitialize event listeners
        function initializeMeritReportEvents() {
            // Reinitialize create merit report button
            const createMeritBtn = document.querySelector('.create-merit-btn');
            if (createMeritBtn) {
                createMeritBtn.onclick = saveMeritReport;
            }
            
            // Add event listeners to update the total amount and summary table when deduction values change
            const taxRateInput = document.getElementById('tax-rate');
            const advancesInput = document.getElementById('advances');
            const insuranceInput = document.getElementById('insurance');
            
            if (taxRateInput) {
                taxRateInput.addEventListener('input', updateTotalAmount);
            }
            
            if (advancesInput) {
                advancesInput.addEventListener('input', updateTotalAmount);
            }
            
            if (insuranceInput) {
                insuranceInput.addEventListener('input', updateTotalAmount);
            }
            
            // Initialize the calculation
            updateTotalAmount();
        }

        // Existing event listener for .select-report buttons should be replaced with the following:
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelectorAll('.select-report').forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault(); // Prevent form submission
                    if (!this.classList.contains('disabled')) {
                        // Get the closest form element of the clicked button
                        const formEl = this.closest('form');
                        // Retrieve both report and contract from the same form
                        const reportId = formEl.querySelector('[name="report_id"]').value;
                        const contractId = formEl.querySelector('[name="contract_id"]').value;
                        const projectId = document.getElementById('project_id').value;
                        selectReport(reportId, contractId, projectId);
                    }
                });
            });
        });

        // Also update the form submission handler to prevent default behavior
        document.addEventListener('DOMContentLoaded', function() {
            const forms = document.querySelectorAll('form');
            forms.forEach(form => {
                form.addEventListener('submit', function(e) {
                    // Only prevent default for report selection forms
                    if (this.querySelector('[name="report_id"]')) {
                        e.preventDefault();
                    }
                });
            });
        });

        // Global variable to track edit mode
        let isEditMode = false;
        let currentMeritReportId = null;

        // Add editReport function
        function editReport(reportId, contractId, projectId) {
            // Show loading state
            showAlert('info', 'جاري تحميل بيانات التقرير للتعديل...');
            
            // Fetch existing merit report data
            fetch(`?action=get_merit_report_for_edit&achievement_report_id=${reportId}`)
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        const data = result.data;
                        
                        // Switch to edit mode
                        isEditMode = true;
                        currentMeritReportId = data.id_merit_reports;
                        
                        // First load the report details section
                        selectReport(reportId, contractId, projectId, () => {
                            // After section is loaded, populate with existing data
                            populateEditForm(data);
                            
                            // Update UI for edit mode
                            updateUIForEditMode();
                            
                            showAlert('success', 'تم تحميل بيانات التقرير للتعديل');
                        });
                    } else {
                        showAlert('error', result.message || 'فشل في تحميل بيانات التقرير');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showAlert('error', 'حدث خطأ أثناء تحميل بيانات التقرير');
                });
        }

        // Function to populate form with existing data
        function populateEditForm(data) {
            // Wait a bit for the DOM to be ready
            setTimeout(() => {
                // Populate deduction fields
                const taxRateInput = document.getElementById('tax-rate');
                const advancesInput = document.getElementById('advances');
                const insuranceInput = document.getElementById('insurance');
                
                if (taxRateInput) taxRateInput.value = data.tax_rate || 0;
                if (advancesInput) advancesInput.value = data.predecessor || 0;
                if (insuranceInput) insuranceInput.value = data.Insurance || 0;
                
                // Update calculations
                updateTotalAmount();
            }, 100);
        }

        // Function to update UI for edit mode
        function updateUIForEditMode() {
            const createBtn = document.querySelector('.create-merit-btn');
            const cancelBtn = document.querySelector('.cancel-edit-btn');

            if (createBtn) {
                createBtn.innerHTML = '<i class="bi bi-arrow-repeat me-2"></i>تحديث تقرير الاستحقاق';
                createBtn.setAttribute('data-mode', 'edit');
                createBtn.classList.remove('btn-primary');
                createBtn.classList.add('btn-warning');
            }

            if (cancelBtn) {
                cancelBtn.style.display = 'inline-block';
            }

            // Add edit mode indicator
            const sectionFour = document.getElementById('section-four');
            if (sectionFour) {
                const existingIndicator = sectionFour.querySelector('.edit-mode-indicator');
                if (!existingIndicator) {
                    const indicator = document.createElement('div');
                    indicator.className = 'edit-mode-indicator';
                    indicator.innerHTML = `
                        <div class="alert-heading">
                            <i class="bi bi-pencil-square me-2"></i>وضع التعديل
                        </div>
                        <p class="alert-text">أنت الآن في وضع تعديل تقرير الاستحقاق. يمكنك تعديل القيم وحفظ التغييرات.</p>
                    `;
                    sectionFour.insertBefore(indicator, sectionFour.firstChild);
                }
            }
        }

        // Function to cancel edit mode
        function cancelEdit() {
            isEditMode = false;
            currentMeritReportId = null;

            // Reset UI
            const createBtn = document.querySelector('.create-merit-btn');
            const cancelBtn = document.querySelector('.cancel-edit-btn');

            if (createBtn) {
                createBtn.innerHTML = '<i class="bi bi-plus-circle me-2"></i>إنشاء تقرير الاستحقاق';
                createBtn.setAttribute('data-mode', 'create');
                createBtn.classList.remove('btn-warning');
                createBtn.classList.add('btn-primary');
            }

            if (cancelBtn) {
                cancelBtn.style.display = 'none';
            }

            // Remove edit mode indicator
            const editIndicator = document.querySelector('.edit-mode-indicator');
            if (editIndicator) {
                editIndicator.remove();
            }

            // Hide section-four
            const sectionFour = document.getElementById('section-four');
            if (sectionFour) {
                sectionFour.classList.add('fade-out');
                setTimeout(() => {
                    sectionFour.style.display = 'none';
                    sectionFour.classList.remove('fade-out');
                }, 500);
            }

            showAlert('info', 'تم إلغاء وضع التعديل');
        }

        // Add saveMeritReport function
        function saveMeritReport() {
            // Get all the required data
            const projectNumber = document.getElementById('project-number').textContent.trim();
            const contractNumber = document.getElementById('contract-number').textContent.trim();
            const extensionNumber = document.getElementById('extension-number').textContent.trim();
            const reportNumber = document.getElementById('report-number').textContent.trim();
            const dailyWage = document.getElementById('daily-wage').textContent.trim();
            const workingDays = document.getElementById('working-days').textContent.trim();
            const baseTotal = document.getElementById('total-amount').textContent.trim();
            const finalTotal = document.getElementById('final-total-amount').textContent.trim();
            
            // Get the deduction values
            const taxRate = parseFloat(document.getElementById('tax-rate').value) || 0;
            const advances = parseFloat(document.getElementById('advances').value) || 0;
            const insurance = parseFloat(document.getElementById('insurance').value) || 0;
            
            // Parse data with proper validation
            const data = {
                id_achievement_reports: reportNumber === '-' ? 0 : parseInt(reportNumber),
                id_Project: projectNumber === '-' ? 0 : parseInt(projectNumber),
                id_contract: contractNumber === '-' ? 0 : parseInt(contractNumber),
                id_extension_contract: extensionNumber === '-' ? null : parseInt(extensionNumber),
                actual_working_days: workingDays === '-' ? 0 : parseInt(workingDays),
                today_wage: dailyWage === '-' ? 0 : parseFloat(dailyWage.replace(/,/g, '')),
                // Base total (before deductions)
                total: baseTotal === '-' ? 0 : parseFloat(baseTotal.replace(/,/g, '')),
                // Total after discounts/deductions
                total_after_discount: finalTotal === '-' ? 0 : parseFloat(finalTotal.replace(/,/g, '')),
                // Deduction values
                tax_rate: taxRate,
                advances: advances,
                insurance: insurance
            };
            
            // Add merit report ID for edit mode
            if (isEditMode && currentMeritReportId) {
                data.id_merit_reports = currentMeritReportId;
            }

            // Validate data
            const validationErrors = [];
            
            // For edit mode, validate merit report ID
            if (isEditMode && (!currentMeritReportId || currentMeritReportId <= 0)) {
                validationErrors.push('رقم تقرير الاستحقاق غير صالح');
            }
            
            // For create mode, validate achievement report data
            if (!isEditMode) {
                if (isNaN(data.id_achievement_reports) || data.id_achievement_reports <= 0) validationErrors.push('رقم تقرير الإنجاز غير صالح');
                if (isNaN(data.id_Project) || data.id_Project <= 0) validationErrors.push('رقم المشروع غير صالح');
                if (isNaN(data.id_contract) || data.id_contract <= 0) validationErrors.push('رقم العقد غير صالح');
            }
            
            // Common validations for both modes
            if (isNaN(data.actual_working_days) || data.actual_working_days <= 0) validationErrors.push('عدد أيام العمل غير صالح');
            if (isNaN(data.today_wage) || data.today_wage <= 0) validationErrors.push('الأجر اليومي غير صالح');
            if (isNaN(data.total) || data.total <= 0) validationErrors.push('إجمالي المبلغ غير صالح');
            
            // If we have id_extension_contract, validate it
            if (data.id_extension_contract !== null && (isNaN(data.id_extension_contract) || data.id_extension_contract <= 0)) {
                validationErrors.push('رقم التمديد غير صالح');
            }

            if (validationErrors.length > 0) {
                showAlert('error', 'أخطاء في البيانات:\n' + validationErrors.join('\n'));
                return;
            }

            // Disable the save button and show loading state
            const saveButton = document.querySelector('.create-merit-btn');
            const originalButtonHtml = saveButton.innerHTML;
            saveButton.disabled = true;
            
            if (isEditMode) {
                saveButton.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>جاري تحديث تقرير الاستحقاق...';
            } else {
                saveButton.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>جاري إنشاء تقرير الاستحقاق...';
            }

            // Determine which endpoint to use
            const endpoint = isEditMode ? '?action=update_merit_report' : '?action=save_merit_report';

            // Send data to server
            fetch(endpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            })
            .then(response => {
                // Check if response is ok
                if (!response.ok) {
                    throw new Error(`Server returned ${response.status}: ${response.statusText}`);
                }
                
                // Check Content-Type to ensure we're getting JSON
                const contentType = response.headers.get('content-type');
                if (!contentType || !contentType.includes('application/json')) {
                    // Log the response text for debugging
                    return response.text().then(text => {
                        console.error('Received non-JSON response:', text);
                        throw new Error('Received non-JSON response from server');
                    });
                }
                
                return response.json();
            })
            .then(result => {
                if (result.success) {
                    showAlert('success', result.message);

                    if (isEditMode) {
                        // For edit mode, show success message and automatically close the form
                        setTimeout(() => {
                            // Hide section-four with fade effect
                            const sectionFour = document.getElementById('section-four');
                            if (sectionFour) {
                                sectionFour.classList.add('fade-out');
                                setTimeout(() => {
                                    sectionFour.style.display = 'none';
                                    sectionFour.classList.remove('fade-out');

                                    // Reset edit mode after form is hidden
                                    isEditMode = false;
                                    currentMeritReportId = null;
                                }, 500);
                            }
                        }, 1500); // Wait 1.5 seconds before closing to let user see the success message
                    } else {
                        // For create mode, update the button state as before
                        const reportButton = document.querySelector(`button[onclick*="selectReport('${data.id_achievement_reports}'"]`);
                        if (reportButton) {
                            const reportContainer = reportButton.closest('.achievement-report-main');

                            // Find the parent container and replace the buttons
                            const buttonContainer = reportButton.closest('.d-flex.align-items-center.gap-2');
                            if (buttonContainer) {
                                buttonContainer.innerHTML = `
                                    <button type="button"
                                            class="btn btn-success disabled"
                                            disabled>
                                        <i class="bi bi-check-circle me-2"></i>
                                        تم إنشاء التقرير
                                    </button>
                                    <button type="button"
                                            class="btn btn-outline-primary btn-edit-report"
                                            title="تعديل التقرير"
                                            onclick="editReport('${data.id_achievement_reports}', '${data.id_contract ? 'contract_' + data.id_contract : ''}', '${data.id_Project}')">
                                        <i class="bi bi-pencil"></i>
                                    </button>
                                `;
                            }

                            // Add success animation class
                            reportContainer.classList.add('highlight-success');

                            // Add used-report class to container
                            reportContainer.classList.add('used-report');

                            // Remove any existing selected state
                            reportContainer.classList.remove('selected');
                        }

                        // Hide section-four with fade effect
                        const sectionFour = document.getElementById('section-four');
                        if (sectionFour) {
                            sectionFour.classList.add('fade-out');
                            setTimeout(() => {
                                sectionFour.style.display = 'none';
                                sectionFour.classList.remove('fade-out');
                            }, 500);
                        }
                    }

                } else {
                    throw new Error(result.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                let errorMessage = error.message || 'حدث خطأ أثناء حفظ البيانات';

                // Handle the specific JSON parsing error
                if (errorMessage.includes('Unexpected token') ||
                    errorMessage.includes('JSON')) {
                    errorMessage = 'خطأ في استجابة الخادم: تنسيق البيانات غير صحيح';
                }

                // Show different error messages based on mode
                if (isEditMode) {
                    showAlert('error', 'فشل في تحديث تقرير الاستحقاق: ' + errorMessage);
                } else {
                    showAlert('error', 'فشل في إنشاء تقرير الاستحقاق: ' + errorMessage);
                }
            })
            .finally(() => {
                // Re-enable the save button
                saveButton.disabled = false;
                saveButton.innerHTML = originalButtonHtml;
            });
        }

        // Helper function to show alerts
        function showAlert(type, message) {
            const alert = document.createElement('div');
            alert.className = `custom-alert ${type}`;
            alert.textContent = message;

            const container = document.querySelector('.container-fluid');
            container.insertBefore(alert, container.firstChild);

            // Auto-hide alert after 5 seconds
            setTimeout(() => {
                alert.classList.add('fade-out');
                setTimeout(() => alert.remove(), 500);
            }, 5000);
        }

        // Remove the previous event listener for create-merit-btn
        const createMeritBtn = document.querySelector('.create-merit-btn');
        if (createMeritBtn) {
            const newCreateMeritBtn = createMeritBtn.cloneNode(true);
            createMeritBtn.parentNode.replaceChild(newCreateMeritBtn, createMeritBtn);
        }
    </script>
    <!-- Add this JavaScript code after the existing script tags -->
    <script>
        // Add event listeners for radio buttons
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelectorAll('.report-radio').forEach(radio => {
                radio.addEventListener('change', function() {
                    if (this.checked && !this.disabled) {
                        const reportId = this.value;
                        const contractId = this.dataset.contractId;
                        const projectId = this.dataset.projectId;
                        selectReport(reportId, contractId, projectId);
                    }
                });
            });
        });

        // Update the selectReport function (this is an alternative implementation, keeping for consistency)
        function selectReportAlternative(reportId, contractId, projectId) {
            // Show loading state
            const sectionFour = document.querySelector('#section-four');
            if (sectionFour) {
                sectionFour.style.display = 'block';
                sectionFour.innerHTML = `
                    <div class="text-center py-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                        <div class="mt-3">جاري تحميل تفاصيل التقرير...</div>
                    </div>
                `;
            }

            // Create form data
            const formData = new FormData();
            formData.append('contract_id', contractId);
            formData.append('report_id', reportId);
            formData.append('project_id', projectId);
            
            // Send request
            fetch(window.location.pathname, {
                method: 'POST',
                body: formData
            })
            .then(response => response.text())
            .then(html => {
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = html;
                
                const newSectionFour = tempDiv.querySelector('#section-four');
                
                if (newSectionFour) {
                    const currentSectionFour = document.querySelector('#section-four');
                    currentSectionFour.innerHTML = newSectionFour.innerHTML;
                    currentSectionFour.style.display = 'block';
                    
                    // Update project number in summary
                    document.getElementById('project-number').textContent = projectId || '-';
                    
                    // Smooth-scroll to section-four
                    currentSectionFour.scrollIntoView({ 
                        behavior: 'smooth',
                        block: 'start'
                    });

                    // Reinitialize event listeners
                    initializeMeritReportEvents();
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('error', 'حدث خطأ أثناء تحديث البيانات');
            });
        }
    </script>
    <script>
        // Update the total amount calculation function
        function updateTotalAmount() {
            // Get the base total amount (wage * days)
            const baseTotal = parseFloat(document.getElementById('base-total-amount').textContent.replace(/,/g, '')) || 0;
            
            // Get deduction values
            const taxRate = parseFloat(document.getElementById('tax-rate').value) || 0;
            const advances = parseFloat(document.getElementById('advances').value) || 0;
            const insurance = parseFloat(document.getElementById('insurance').value) || 0;

            // Calculate deductions
            // 1. First calculate tax on the base amount
            const taxAmount = (baseTotal * taxRate) / 100;
            let remainingAmount = baseTotal - taxAmount;
            
            // 2. Then subtract advances
            remainingAmount -= advances;
            
            // 3. Finally subtract insurance
            remainingAmount -= insurance;

            // Ensure the final amount is not negative
            const finalAmount = Math.max(0, remainingAmount);

            // Format all amounts with thousands separator
            const formatNumber = (num) => {
                return num.toLocaleString('en-US', {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2
                });
            };

            // Update the UI with both the base total and final amount after deductions
            document.getElementById('final-total-amount').textContent = formatNumber(finalAmount);
            
            // Update all the values in the Data Summary table
            document.getElementById('total-amount').textContent = formatNumber(baseTotal);
            document.getElementById('summary-net-amount').textContent = formatNumber(finalAmount);
            document.getElementById('summary-tax-rate').textContent = formatNumber(taxRate);
            document.getElementById('summary-advances').textContent = formatNumber(advances);
            document.getElementById('summary-insurance').textContent = formatNumber(insurance);
        }

        // Initialize the calculation when the page loads
        document.addEventListener('DOMContentLoaded', function() {
            updateTotalAmount();
            
            // Add event listeners to update the total amount when deduction values change
            document.getElementById('tax-rate').addEventListener('input', updateTotalAmount);
            document.getElementById('advances').addEventListener('input', updateTotalAmount);
            document.getElementById('insurance').addEventListener('input', updateTotalAmount);
        });
    </script>
</body>
</html>